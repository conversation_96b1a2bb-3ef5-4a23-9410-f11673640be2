<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;%@ taglib prefix="my" uri="/WEB-INF/jsp/jsp2-example-taglib.tld" %>
&lt;html>
  &lt;head>
    &lt;title>JSP 2.0 Examples - Book SimpleTag Handler&lt;/title>
  &lt;/head>
  &lt;body>
    &lt;h1>JSP 2.0 Examples - Book SimpleTag Handler&lt;/h1>
    &lt;hr>
    &lt;p>Illustrates a semi-realistic use of SimpleTag and the Expression
    Language.  First, a &amp;lt;my:findBook&amp;gt; tag is invoked to populate
    the page context with a BookBean.  Then, the books fields are printed
    in all caps.&lt;/p>
    &lt;br>
    &lt;b>&lt;u>Result:&lt;/u>&lt;/b>&lt;br>
    &lt;my:findBook var="book"/>
    &lt;table border="1">
        &lt;thead>
        &lt;td>&lt;b>Field&lt;/b>&lt;/td>
        &lt;td>&lt;b>Value&lt;/b>&lt;/td>
        &lt;td>&lt;b>Capitalized&lt;/b>&lt;/td>
    &lt;/thead>
    &lt;tr>
        &lt;td>Title&lt;/td>
        &lt;td>${book.title}&lt;/td>
        &lt;td>${my:caps(book.title)}&lt;/td>
    &lt;/tr>
    &lt;tr>
        &lt;td>Author&lt;/td>
        &lt;td>${book.author}&lt;/td>
        &lt;td>${my:caps(book.author)}&lt;/td>
    &lt;/tr>
    &lt;tr>
        &lt;td>ISBN&lt;/td>
        &lt;td>${book.isbn}&lt;/td>
        &lt;td>${my:caps(book.isbn)}&lt;/td>
    &lt;/tr>
    &lt;/table>
  &lt;/body>
&lt;/html>
</pre></body></html>