02-Jul-2025 13:15:11.397 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 13:15:11.397 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 13:15:11.398 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@32f232a5')
02-Jul-2025 13:27:37.726 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 13:27:37.726 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 13:27:37.727 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@6aa3a905')
02-Jul-2025 13:34:01.138 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 13:34:01.138 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 13:34:01.139 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@2250b9f2')
02-Jul-2025 13:35:09.559 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 13:35:09.559 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 13:35:09.561 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@2250b9f2')
02-Jul-2025 13:48:15.249 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 13:48:15.249 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 13:48:15.250 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@2250b9f2')
02-Jul-2025 13:49:43.188 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 13:49:43.188 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 13:49:43.189 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@3d08f3f5')
02-Jul-2025 14:09:54.340 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:09:54.340 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:09:54.341 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@40dd3977')
02-Jul-2025 14:11:51.529 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:11:51.529 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:11:51.530 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@841e575')
02-Jul-2025 14:18:12.161 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:18:12.161 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:18:12.163 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@89ff02e')
02-Jul-2025 14:18:12.255 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:18:12.255 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 14:18:26.681 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:18:26.682 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:18:26.688 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@6256ac4f')
02-Jul-2025 14:18:26.780 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:18:26.780 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 14:18:31.249 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:18:31.249 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:18:31.250 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@25ddbbbb')
02-Jul-2025 14:18:31.332 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:18:31.332 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 14:18:35.814 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:18:35.814 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:18:35.816 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@1536602f')
02-Jul-2025 14:18:35.897 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:18:35.897 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 14:19:08.243 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:19:08.243 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:19:08.244 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@5f2f577')
02-Jul-2025 14:20:21.481 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:20:21.481 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:20:21.483 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@4e76dac')
02-Jul-2025 14:42:30.316 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:42:30.316 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:42:30.318 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@25ddbbbb')
02-Jul-2025 14:42:30.410 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:42:30.410 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 14:42:48.228 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:42:48.229 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:42:48.230 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@76c7beb3')
02-Jul-2025 14:44:15.051 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:44:15.051 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 14:44:45.365 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:44:45.365 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:44:45.366 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@5bda80bf')
02-Jul-2025 14:45:36.231 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:45:36.231 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 14:47:22.753 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:47:22.753 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:47:22.754 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@493dfb8e')
02-Jul-2025 14:49:01.141 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:49:01.141 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 14:49:38.947 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:49:38.947 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:49:38.948 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@4ef27d66')
02-Jul-2025 14:52:20.059 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:52:20.059 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 14:55:27.208 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:55:27.208 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:55:27.209 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@493dfb8e')
02-Jul-2025 14:56:11.464 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:56:11.464 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 14:56:29.479 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:56:29.479 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:56:29.481 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@303e3593')
02-Jul-2025 14:57:02.432 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:57:02.432 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 14:57:09.134 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 14:57:09.134 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 14:57:09.135 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@5d5d9e5')
02-Jul-2025 14:59:27.597 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 14:59:27.598 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 15:00:25.232 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 15:00:25.232 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 15:00:25.233 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@224b4d61')
02-Jul-2025 15:01:34.168 SEVERE [http-nio-8080-exec-8] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:01:48.487 SEVERE [http-nio-8080-exec-10] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:01:57.729 SEVERE [http-nio-8080-exec-2] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:02:13.529 SEVERE [http-nio-8080-exec-1] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:02:36.820 SEVERE [http-nio-8080-exec-6] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:04:20.287 SEVERE [http-nio-8080-exec-4] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:04:56.905 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
02-Jul-2025 15:04:56.905 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
02-Jul-2025 15:05:01.697 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
02-Jul-2025 15:05:01.697 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
02-Jul-2025 15:05:01.699 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@303e3593')
02-Jul-2025 15:07:50.724 SEVERE [http-nio-8080-exec-4] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:09:16.339 SEVERE [http-nio-8080-exec-2] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:11:01.540 SEVERE [http-nio-8080-exec-6] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:13:36.752 SEVERE [http-nio-8080-exec-6] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:13:36.753 SEVERE [http-nio-8080-exec-2] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:15:29.470 SEVERE [http-nio-8080-exec-6] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:18:03.092 SEVERE [http-nio-8080-exec-5] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:18:20.839 SEVERE [http-nio-8080-exec-1] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:18:25.575 SEVERE [http-nio-8080-exec-7] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() for servlet [ProxyServlet] in context with path [/jv2] threw exception
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
