<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;html>
&lt;jsp:useBean id="cart" scope="session" class="sessions.DummyCart" />

&lt;jsp:setProperty name="cart" property="*" />
&lt;%
    cart.processRequest();
%>


&lt;FONT size = 5 COLOR="#CC0000">
&lt;br> You have the following items in your cart:
&lt;ol>
&lt;%
    Item[] items = cart.getItems();
    for (Item item : items) {
%>
&lt;li> &lt;% out.print(util.HTMLFilter.filter(item.getTitle())); %>
&lt;%
    }
%>
&lt;/ol>

&lt;/FONT>

&lt;hr>
&lt;%@ include file ="shopping.jsp" %>
&lt;/html>
</pre></body></html>