{"version": "0.2.0", "configurations": [{"type": "java", "name": "Debug ProxyServlet with Tomcat", "request": "launch", "mainClass": "org.apache.catalina.startup.Bootstrap", "args": ["start"], "vmArgs": ["-Dcatalina.home=${workspaceFolder}/tomcat", "-Dcatalina.base=${workspaceFolder}/tomcat", "-Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager", "-Djava.util.logging.config.file=${workspaceFolder}/tomcat/conf/logging.properties", "-Duntill.home=${workspaceFolder}/test-config"], "classPaths": ["${workspaceFolder}/build-gradle/libs/*.war", "${workspaceFolder}/tomcat/bin/bootstrap.jar", "${workspaceFolder}/tomcat/bin/tomcat-juli.jar", "${workspaceFolder}/tomcat/lib/*.jar"], "console": "internalConsole", "internalConsoleOptions": "openOnSessionStart"}, {"type": "java", "name": "Debug ProxyServlet Tests", "request": "launch", "mainClass": "org.junit.runner.JUnitCore", "args": ["com.untill.ProxyServletTest"], "vmArgs": ["-Duntill.home=${workspaceFolder}/test-config"], "classPaths": ["${workspaceFolder}/build-gradle/classes/java/main", "${workspaceFolder}/build-gradle/classes/java/test", "${workspaceFolder}/build-gradle/libs/*.jar"], "console": "internalConsole"}, {"type": "java", "name": "Debug Gradle Build", "request": "launch", "mainClass": "org.gradle.wrapper.GradleWrapperMain", "args": ["build", "--debug"], "vmArgs": ["-Duntill.home=${workspaceFolder}/test-config"], "cwd": "${workspaceFolder}", "console": "internalConsole"}]}