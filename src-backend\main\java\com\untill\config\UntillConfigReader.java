package com.untill.config;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Utility class for reading unTill configuration from untill.ini file
 */
public final class UntillConfigReader {
	private static final Logger LOGGER = Logger.getLogger(UntillConfigReader.class.getName());

	private static final String UNTILL_HOME_PROPERTY = "untill.home";
	private static final String CONFIG_FILE_NAME = "untill.ini";
	private static final String COMMON_SECTION = "common";
	private static final String PORT_PARAMETER = "port";
	private static final int PORT_OFFSET = 5;
	private static final int DEFAULT_BASE_PORT = 3060;

	private static UntillConfig cachedConfig;

	/**
	 * Private constructor to prevent instantiation
	 */
	private UntillConfigReader() {
		// Utility class
	}

	/**
	 * Get unTill configuration
	 */
	public static UntillConfig getConfig() {
		if (cachedConfig == null) {
			cachedConfig = loadConfig();
		}
		return cachedConfig;
	}

	/**
	 * Force reload configuration from file
	 */
	public static UntillConfig reloadConfig() {
		cachedConfig = loadConfig();
		return cachedConfig;
	}

	/**
	 * Get the untill.home directory
	 */
	public static String getUntillHome() {
		String untillHome = System.getProperty(UNTILL_HOME_PROPERTY);
		if (untillHome == null || untillHome.trim().isEmpty()) {
			untillHome = "C:\\unTill";
		}
		return untillHome;
	}

	private static UntillConfig loadConfig() {
		try {
			String untillHome = System.getProperty(UNTILL_HOME_PROPERTY);
			if (untillHome == null || untillHome.trim().isEmpty()) {
				untillHome = "C:\\unTill";
			}

			Path configPath = Paths.get(untillHome, CONFIG_FILE_NAME);
			if (!Files.exists(configPath)) {
				LOGGER.warning("Configuration file not found: " + configPath + ". Using default configuration.");
				return createDefaultConfig();
			}

			LOGGER.info("Reading configuration from: " + configPath);

			String portStr = parseIniFile(configPath, COMMON_SECTION, PORT_PARAMETER);

			int basePort = DEFAULT_BASE_PORT;
			if (portStr != null) {
				try {
					basePort = Integer.parseInt(portStr.trim());
					LOGGER.info("Read base port from config: " + basePort);
				} catch (NumberFormatException e) {
					LOGGER.log(Level.WARNING, "Invalid port value in config: " + portStr + ". Using default.", e);
				}
			} else {
				LOGGER.warning("Port parameter not found in config. Using default: " + basePort);
			}

			int targetPort = basePort + PORT_OFFSET;
			String targetHost = "localhost";

			LOGGER.info("Configuration loaded - Host: " + targetHost + ", Port: " + targetPort);

			return new UntillConfig(targetHost, targetPort, configPath.toString());

		} catch (Exception e) {
			LOGGER.log(Level.SEVERE, "Error reading configuration. Using default.", e);
			return createDefaultConfig();
		}
	}

	private static UntillConfig createDefaultConfig() {
		String defaultHost = "localhost";
		int defaultPort = DEFAULT_BASE_PORT + PORT_OFFSET;
		LOGGER.info("Using default configuration - Host: " + defaultHost + ", Port: " + defaultPort);
		return new UntillConfig(defaultHost, defaultPort, "default");
	}

	/**
	 * Parse INI file and extract value from specific section and key
	 */
	private static String parseIniFile(Path filePath, String section, String key) {
		try {
			String currentSection = null;
			for (String line : Files.readAllLines(filePath)) {
				line = line.trim();

				// Skip empty lines and comments
				if (line.isEmpty() || line.startsWith("#") || line.startsWith(";")) {
					continue;
				}

				// Check for section header
				if (line.startsWith("[") && line.endsWith("]")) {
					currentSection = line.substring(1, line.length() - 1);
					continue;
				}

				// Check for key=value in the target section
				if (section.equalsIgnoreCase(currentSection) && line.contains("=")) {
					String[] parts = line.split("=", 2);
					if (parts.length == 2 && key.equalsIgnoreCase(parts[0].trim())) {
						return parts[1].trim();
					}
				}
			}
		} catch (IOException e) {
			LOGGER.log(Level.WARNING, "Error reading INI file: " + filePath, e);
		}
		return null;
	}

	/**
	 * Configuration data class
	 */
	public static class UntillConfig {
		private final String host;
		private final int port;
		private final String configSource;

		public UntillConfig(String host, int port, String configSource) {
			this.host = host;
			this.port = port;
			this.configSource = configSource;
		}

		public String getHost() {
			return host;
		}

		public int getPort() {
			return port;
		}

		public String getConfigSource() {
			return configSource;
		}

		public String getTargetUrl() {
			return "http://" + host + ":" + port;
		}

		@Override
		public String toString() {
			return "UntillConfig{host='" + host + "', port=" + port + ", source='" + configSource + "'}";
		}
	}
}
