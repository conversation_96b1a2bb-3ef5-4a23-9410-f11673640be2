export const ACTION_GLOBAL_WINDOW_RESIZE = 'windowResize';
export const ACTION_GLOBAL_SET_STRINGS = 'setStrings';

export const ACTION_SWITCH_TAB = 'switchTab';
export const ACTION_NEW_TAB = 'newTab';
export const ACTION_ADD_TAB_FILTER = 'addTabFilter';

export const ACTION_NEWCONN_CHANGE_PROP = 'newConnChangeProp';
export const ACTION_NEWCONN_START_CONNECTING = 'newConnStartConnecting';
export const ACTION_NEWCONN_CONNECT_ERROR = 'newConnError';
export const ACTION_NEWCONN_CONNECT_SUCCESS = 'newConnSuccess';

export const ACTION_DATABASE_OPENED = 'databaseOpened';
export const ACTION_DATABASE_CLOSE = 'databaseClose';

export const ACTION_DATABASE_JOURNAL_LOADING_STARTED = 'databaseJournalLoadingStarted';
export const ACTION_DATABASE_JOURNAL_LOADING_ERROR = 'databaseJournalLoadingError';
export const ACTION_DATABASE_JOURNAL_LOADING_SUCCESS = 'databaseJournalLoadingSuccess';

export const ACTION_DATABASE_JOURNAL_OPEN_DETAILS = 'databaseJournalOpenDetails';
export const ACTION_DATABASE_JOURNAL_CLOSE_DETAILS = 'databaseJournalCloseDetails';

export const ACTION_DATABASE_JOURNAL_DROP_RECORS = 'databaseJournalDropRecords';

export const ACTION_PERIOD_SWITCH_KIND = 'periodSwitchKind';
export const ACTION_PERIOD_INC_DAY = 'periodIncDay';
export const ACTION_PERIOD_CHANGE_DAY_VALUE = 'periodChageDayValue';
export const ACTION_PERIOD_CHANGE_RANGE_VALUE = 'periodChageRangeValue';

export const ACTION_FITLERS_CHANGE_VALUE = 'filtersChangeValue';
export const ACTION_FITLERS_CHANGE_VALIDATION = 'filtersChangeValidation';