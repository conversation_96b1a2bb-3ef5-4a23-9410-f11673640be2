<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<html>
<head>
<title>Untitled Document</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>

<body bgcolor="#FFFFFF">
<p><font color="#0000FF"><a href="servlet/CookieExample"><img src="images/execute.gif" align="right" border="0"></a><a href="index.html"><img src="images/return.gif" width="24" height="24" align="right" border="0"></a></font></p>
<h3>Source Code for Cookie Example<font color="#0000FF"><br>
  </font> </h3>
<font color="#0000FF"></font>
<pre><font color="#0000FF">import</font> java.io.*;
<font color="#0000FF">import</font> javax.servlet.*;
<font color="#0000FF">import</font> javax.servlet.http.*;

<font color="#0000FF">public class</font> CookieExample <font color="#0000FF">extends</font> HttpServlet {

    <font color="#0000FF">public void</font> doGet(HttpServletRequest request, HttpServletResponse response)
    <font color="#0000FF">throws</font> IOException, ServletException
    {
        response.setContentType(&quot;<font color="#009900">text/html</font>&quot;);
        PrintWriter out = response.getWriter();

        <font color="#CC0000">// print out cookies</font>

        Cookie[] cookies = request.getCookies();
        for (int i = 0; i &lt; cookies.length; i++) {
            Cookie c = cookies[i];
            String name = c.getName();
            String value = c.getValue();
            out.println(name + &quot;<font color="#009900"> = </font>&quot; + value);
        }

        <font color="#CC0000">// set a cookie</font>

        String name = request.getParameter(&quot;<font color="#009900">cookieName</font>&quot;);
        if (name != null &amp;&amp; name.length() &gt; 0) {
            String value = request.getParameter(&quot;<font color="#009900">cookieValue</font>&quot;);
            Cookie c = new Cookie(name, value);
            response.addCookie(c);
        }
    }
}</pre>
</body>
</html>
