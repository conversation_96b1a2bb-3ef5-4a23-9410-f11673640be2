02-Jul-2025 13:15:10.637 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\Java\jdk1.8.0_211\jre
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_211-b12
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Oracle Corporation
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xrunjdwp:transport=dt_socket,address=localhost:9785,server=n,suspend=y
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2/tomcat/conf/logging.properties
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Duntill.home=C:\PRODUCTS\JViewer2/test-config
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dfile.encoding=UTF-8
02-Jul-2025 13:15:10.645 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 13:15:10.645 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 13:15:10.645 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 13:15:10.650 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 13:15:10.829 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:15:10.839 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [317] milliseconds
02-Jul-2025 13:15:10.858 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 13:15:10.858 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 13:15:10.865 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 13:15:10.897 WARNING [main] org.apache.catalina.webresources.DirResourceSet.initInternal Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] which is part of the web application [/docs]
02-Jul-2025 13:15:10.964 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.026 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.195 WARNING [main] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [147] milliseconds.
02-Jul-2025 13:15:11.210 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [345] ms
02-Jul-2025 13:15:11.210 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 13:15:11.228 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.372 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.404 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [194] ms
02-Jul-2025 13:15:11.404 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 13:15:11.414 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.422 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.428 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [24] ms
02-Jul-2025 13:15:11.428 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 13:15:11.440 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.455 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.457 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [28] ms
02-Jul-2025 13:15:11.457 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 13:15:11.466 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.474 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.476 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [19] ms
02-Jul-2025 13:15:11.480 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:15:11.492 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [652] milliseconds
02-Jul-2025 13:27:37.003 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 13:27:37.005 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 13:27:37.005 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\Java\jdk1.8.0_211\jre
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_211-b12
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Oracle Corporation
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xrunjdwp:transport=dt_socket,address=localhost:10228,server=n,suspend=y
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2/tomcat/conf/logging.properties
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Duntill.home=C:\PRODUCTS\JViewer2/test-config
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dfile.encoding=UTF-8
02-Jul-2025 13:27:37.009 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 13:27:37.009 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 13:27:37.009 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 13:27:37.013 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 13:27:37.190 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:27:37.202 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [306] milliseconds
02-Jul-2025 13:27:37.219 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 13:27:37.219 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 13:27:37.226 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 13:27:37.258 WARNING [main] org.apache.catalina.webresources.DirResourceSet.initInternal Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] which is part of the web application [/docs]
02-Jul-2025 13:27:37.314 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.355 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.530 WARNING [main] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [152] milliseconds.
02-Jul-2025 13:27:37.543 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [317] ms
02-Jul-2025 13:27:37.543 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 13:27:37.561 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.700 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.733 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [190] ms
02-Jul-2025 13:27:37.733 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 13:27:37.742 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.750 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.755 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [22] ms
02-Jul-2025 13:27:37.755 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 13:27:37.767 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.776 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.778 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [23] ms
02-Jul-2025 13:27:37.778 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 13:27:37.788 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.803 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.809 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [31] ms
02-Jul-2025 13:27:37.821 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:27:37.844 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [641] milliseconds
02-Jul-2025 13:34:00.414 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 13:34:00.416 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 13:34:00.416 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 13:34:00.416 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 13:34:00.416 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 13:34:00.416 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 13:34:00.416 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\Java\jdk1.8.0_211\jre
02-Jul-2025 13:34:00.416 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_211-b12
02-Jul-2025 13:34:00.416 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Oracle Corporation
02-Jul-2025 13:34:00.416 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:34:00.416 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:34:00.418 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xrunjdwp:transport=dt_socket,address=localhost:10372,server=n,suspend=y
02-Jul-2025 13:34:00.418 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:34:00.418 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:34:00.418 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 13:34:00.418 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2/tomcat/conf/logging.properties
02-Jul-2025 13:34:00.418 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Duntill.home=C:\PRODUCTS\JViewer2/test-config
02-Jul-2025 13:34:00.418 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dfile.encoding=UTF-8
02-Jul-2025 13:34:00.420 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 13:34:00.420 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 13:34:00.420 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 13:34:00.423 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 13:34:00.618 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:34:00.631 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [334] milliseconds
02-Jul-2025 13:34:00.650 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 13:34:00.650 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 13:34:00.659 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 13:34:00.700 WARNING [main] org.apache.catalina.webresources.DirResourceSet.initInternal Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] which is part of the web application [/docs]
02-Jul-2025 13:34:00.956 WARNING [main] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [129] milliseconds.
02-Jul-2025 13:34:00.970 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [312] ms
02-Jul-2025 13:34:00.970 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 13:34:01.144 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [174] ms
02-Jul-2025 13:34:01.144 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 13:34:01.161 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [17] ms
02-Jul-2025 13:34:01.162 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 13:34:01.179 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [17] ms
02-Jul-2025 13:34:01.179 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 13:34:01.190 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [11] ms
02-Jul-2025 13:34:01.193 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:34:01.204 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [572] milliseconds
02-Jul-2025 13:35:08.822 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 13:35:08.824 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 13:35:08.824 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 13:35:08.824 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 13:35:08.824 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 13:35:08.824 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 13:35:08.824 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\Java\jdk1.8.0_211\jre
02-Jul-2025 13:35:08.824 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_211-b12
02-Jul-2025 13:35:08.824 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Oracle Corporation
02-Jul-2025 13:35:08.824 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:35:08.824 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:35:08.826 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xrunjdwp:transport=dt_socket,address=localhost:10419,server=n,suspend=y
02-Jul-2025 13:35:08.826 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:35:08.826 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:35:08.826 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 13:35:08.826 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2/tomcat/conf/logging.properties
02-Jul-2025 13:35:08.826 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Duntill.home=C:\PRODUCTS\JViewer2/test-config
02-Jul-2025 13:35:08.826 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dfile.encoding=UTF-8
02-Jul-2025 13:35:08.828 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 13:35:08.828 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 13:35:08.828 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 13:35:08.832 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 13:35:09.007 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:35:09.023 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [312] milliseconds
02-Jul-2025 13:35:09.048 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 13:35:09.048 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 13:35:09.057 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 13:35:09.098 WARNING [main] org.apache.catalina.webresources.DirResourceSet.initInternal Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] which is part of the web application [/docs]
02-Jul-2025 13:35:09.354 WARNING [main] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [135] milliseconds.
02-Jul-2025 13:35:09.369 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [311] ms
02-Jul-2025 13:35:09.369 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 13:35:09.567 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [198] ms
02-Jul-2025 13:35:09.568 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 13:35:09.590 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [23] ms
02-Jul-2025 13:35:09.590 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 13:35:09.608 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [18] ms
02-Jul-2025 13:35:09.608 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 13:35:09.622 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [14] ms
02-Jul-2025 13:35:09.624 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:35:09.636 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [613] milliseconds
02-Jul-2025 13:48:14.551 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 13:48:14.553 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 13:48:14.553 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 13:48:14.554 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 13:48:14.554 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 13:48:14.554 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 13:48:14.554 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\Java\jdk1.8.0_211\jre
02-Jul-2025 13:48:14.554 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_211-b12
02-Jul-2025 13:48:14.554 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Oracle Corporation
02-Jul-2025 13:48:14.554 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:48:14.554 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:48:14.556 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xrunjdwp:transport=dt_socket,address=localhost:10710,server=n,suspend=y
02-Jul-2025 13:48:14.556 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:48:14.556 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:48:14.556 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 13:48:14.556 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2/tomcat/conf/logging.properties
02-Jul-2025 13:48:14.556 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Duntill.home=C:\PRODUCTS\JViewer2/test-config
02-Jul-2025 13:48:14.556 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dfile.encoding=UTF-8
02-Jul-2025 13:48:14.559 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 13:48:14.559 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 13:48:14.559 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 13:48:14.563 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 13:48:14.740 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:48:14.752 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [310] milliseconds
02-Jul-2025 13:48:14.769 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 13:48:14.770 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 13:48:14.777 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 13:48:14.809 WARNING [main] org.apache.catalina.webresources.DirResourceSet.initInternal Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] which is part of the web application [/docs]
02-Jul-2025 13:48:15.056 WARNING [main] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [132] milliseconds.
02-Jul-2025 13:48:15.070 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [293] ms
02-Jul-2025 13:48:15.071 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 13:48:15.256 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [186] ms
02-Jul-2025 13:48:15.256 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 13:48:15.274 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [18] ms
02-Jul-2025 13:48:15.274 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 13:48:15.289 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [15] ms
02-Jul-2025 13:48:15.289 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 13:48:15.302 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [13] ms
02-Jul-2025 13:48:15.305 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:48:15.317 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [564] milliseconds
02-Jul-2025 13:49:42.468 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 13:49:42.471 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 13:49:42.473 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 13:49:42.473 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 13:49:42.473 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 13:49:42.473 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 13:49:42.473 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 13:49:42.474 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 13:49:42.474 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 13:49:42.475 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:49:42.475 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:49:42.481 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
02-Jul-2025 13:49:42.482 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
02-Jul-2025 13:49:42.482 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
02-Jul-2025 13:49:42.482 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
02-Jul-2025 13:49:42.483 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util=ALL-UNNAMED
02-Jul-2025 13:49:42.483 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
02-Jul-2025 13:49:42.483 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
02-Jul-2025 13:49:42.483 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2\tomcat\conf\logging.properties
02-Jul-2025 13:49:42.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 13:49:42.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 13:49:42.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 13:49:42.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dsun.io.useCanonCaches=false
02-Jul-2025 13:49:42.485 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,address=localhost:8000,server=y,suspend=n
02-Jul-2025 13:49:42.485 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
02-Jul-2025 13:49:42.485 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:49:42.485 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:49:42.485 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=C:\PRODUCTS\JViewer2\tomcat\temp
02-Jul-2025 13:49:42.489 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 13:49:42.489 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 13:49:42.489 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 13:49:42.493 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 13:49:42.643 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:49:42.665 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [304] milliseconds
02-Jul-2025 13:49:42.696 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 13:49:42.696 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 13:49:42.707 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 13:49:42.947 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [240] ms
02-Jul-2025 13:49:42.947 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 13:49:43.197 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [250] ms
02-Jul-2025 13:49:43.197 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 13:49:43.225 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [28] ms
02-Jul-2025 13:49:43.225 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 13:49:43.247 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [21] ms
02-Jul-2025 13:49:43.247 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 13:49:43.261 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [14] ms
02-Jul-2025 13:49:43.264 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:49:43.310 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [645] milliseconds
02-Jul-2025 13:51:23.330 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\JViewer2-0.6.0-SNAPSHOT.war]
02-Jul-2025 13:51:23.494 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\JViewer2-0.6.0-SNAPSHOT.war] has finished in [164] ms
02-Jul-2025 13:52:23.524 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Undeploying context [/JViewer2-0.6.0-SNAPSHOT]
02-Jul-2025 13:52:23.550 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war]
02-Jul-2025 13:52:23.696 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war] has finished in [146] ms
02-Jul-2025 14:09:53.549 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:09:53.551 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:09:53.551 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:09:53.551 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:09:53.552 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:09:53.552 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:09:53.552 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:09:53.552 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:09:53.552 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:09:53.552 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:09:53.552 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:09:53.557 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
02-Jul-2025 14:09:53.558 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
02-Jul-2025 14:09:53.558 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
02-Jul-2025 14:09:53.558 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
02-Jul-2025 14:09:53.558 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util=ALL-UNNAMED
02-Jul-2025 14:09:53.559 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
02-Jul-2025 14:09:53.559 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
02-Jul-2025 14:09:53.559 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2\tomcat\conf\logging.properties
02-Jul-2025 14:09:53.559 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:09:53.559 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:09:53.561 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:09:53.561 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dsun.io.useCanonCaches=false
02-Jul-2025 14:09:53.561 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,address=localhost:8000,server=y,suspend=n
02-Jul-2025 14:09:53.561 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
02-Jul-2025 14:09:53.561 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:09:53.562 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:09:53.562 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=C:\PRODUCTS\JViewer2\tomcat\temp
02-Jul-2025 14:09:53.564 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:09:53.564 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:09:53.564 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:09:53.568 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:09:53.701 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:09:53.718 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [274] milliseconds
02-Jul-2025 14:09:53.747 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:09:53.747 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:09:53.769 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war]
02-Jul-2025 14:09:54.093 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war] has finished in [324] ms
02-Jul-2025 14:09:54.093 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:09:54.118 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [25] ms
02-Jul-2025 14:09:54.118 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:09:54.349 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [231] ms
02-Jul-2025 14:09:54.350 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:09:54.371 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [21] ms
02-Jul-2025 14:09:54.371 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:09:54.390 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [19] ms
02-Jul-2025 14:09:54.390 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:09:54.406 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [15] ms
02-Jul-2025 14:09:54.410 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:09:54.456 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [738] milliseconds
02-Jul-2025 14:11:50.797 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:11:50.799 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:11:50.800 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:11:50.800 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:11:50.800 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:11:50.800 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:11:50.800 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:11:50.800 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:11:50.800 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:11:50.800 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:11:50.801 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:11:50.805 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
02-Jul-2025 14:11:50.806 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
02-Jul-2025 14:11:50.806 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
02-Jul-2025 14:11:50.806 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
02-Jul-2025 14:11:50.806 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util=ALL-UNNAMED
02-Jul-2025 14:11:50.806 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
02-Jul-2025 14:11:50.807 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
02-Jul-2025 14:11:50.807 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2\tomcat\conf\logging.properties
02-Jul-2025 14:11:50.808 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:11:50.808 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:11:50.808 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:11:50.808 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dsun.io.useCanonCaches=false
02-Jul-2025 14:11:50.809 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,address=localhost:8000,server=y,suspend=n
02-Jul-2025 14:11:50.809 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
02-Jul-2025 14:11:50.809 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:11:50.809 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:11:50.809 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=C:\PRODUCTS\JViewer2\tomcat\temp
02-Jul-2025 14:11:50.812 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:11:50.812 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:11:50.812 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:11:50.816 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:11:50.976 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:11:50.995 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [308] milliseconds
02-Jul-2025 14:11:51.025 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:11:51.025 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:11:51.035 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war]
02-Jul-2025 14:11:51.269 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war] has finished in [234] ms
02-Jul-2025 14:11:51.270 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:11:51.299 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [28] ms
02-Jul-2025 14:11:51.299 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:11:51.537 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [238] ms
02-Jul-2025 14:11:51.538 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:11:51.564 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [27] ms
02-Jul-2025 14:11:51.565 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:11:51.587 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [22] ms
02-Jul-2025 14:11:51.588 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:11:51.605 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [17] ms
02-Jul-2025 14:11:51.608 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:11:51.655 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [659] milliseconds
02-Jul-2025 14:13:11.654 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\JViewer2]
02-Jul-2025 14:13:11.685 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\JViewer2] has finished in [31] ms
02-Jul-2025 14:15:22.303 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Undeploying context [/JViewer2]
02-Jul-2025 14:17:42.396 INFO [Catalina-utility-2] org.apache.catalina.users.MemoryUserDatabase.backgroundProcess Reloading memory user database [UserDatabase] from updated source [file:/C:/PRODUCTS/JViewer2/tomcat/conf/tomcat-users.xml]
02-Jul-2025 14:18:11.452 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:18:11.454 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:18:11.455 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:18:11.455 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:18:11.455 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:18:11.455 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:18:11.455 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:18:11.455 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:18:11.455 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:18:11.456 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:11.456 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:11.462 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
02-Jul-2025 14:18:11.462 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
02-Jul-2025 14:18:11.462 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
02-Jul-2025 14:18:11.462 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
02-Jul-2025 14:18:11.462 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util=ALL-UNNAMED
02-Jul-2025 14:18:11.463 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
02-Jul-2025 14:18:11.463 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
02-Jul-2025 14:18:11.463 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2\tomcat\conf\logging.properties
02-Jul-2025 14:18:11.463 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:18:11.463 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:18:11.463 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:18:11.465 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dsun.io.useCanonCaches=false
02-Jul-2025 14:18:11.465 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
02-Jul-2025 14:18:11.465 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:11.465 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:11.466 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=C:\PRODUCTS\JViewer2\tomcat\temp
02-Jul-2025 14:18:11.468 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:18:11.468 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:18:11.468 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:18:11.472 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:18:11.623 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:11.638 SEVERE [main] org.apache.catalina.util.LifecycleBase.handleSubClassException Failed to initialize component [Connector["http-nio-8080"]]
	org.apache.catalina.LifecycleException: Protocol handler initialization failed
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1072)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardService.initInternal(StandardService.java:522)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardServer.initInternal(StandardServer.java:986)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:690)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:713)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.apache.catalina.startup.Bootstrap.load(Bootstrap.java:302)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:475)
	Caused by: java.net.BindException: Address already in use: bind
		at java.base/sun.nio.ch.Net.bind0(Native Method)
		at java.base/sun.nio.ch.Net.bind(Net.java:555)
		at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:337)
		at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:294)
		at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:298)
		at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:253)
		at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1465)
		at org.apache.tomcat.util.net.AbstractEndpoint.init(AbstractEndpoint.java:1478)
		at org.apache.coyote.AbstractProtocol.init(AbstractProtocol.java:663)
		at org.apache.coyote.http11.AbstractHttp11Protocol.init(AbstractHttp11Protocol.java:80)
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1070)
		... 13 more
02-Jul-2025 14:18:11.639 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [289] milliseconds
02-Jul-2025 14:18:11.667 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:18:11.667 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:18:11.677 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war]
02-Jul-2025 14:18:11.908 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war] has finished in [231] ms
02-Jul-2025 14:18:11.909 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:18:11.935 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [26] ms
02-Jul-2025 14:18:11.935 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:18:12.171 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [236] ms
02-Jul-2025 14:18:12.172 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:18:12.195 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [23] ms
02-Jul-2025 14:18:12.196 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:18:12.216 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [20] ms
02-Jul-2025 14:18:12.217 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:18:12.235 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [17] ms
02-Jul-2025 14:18:12.240 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [601] milliseconds
02-Jul-2025 14:18:12.246 SEVERE [main] org.apache.catalina.core.StandardServer.await Failed to create server shutdown socket on address [localhost] and port [8005] (base port [8005] and offset [0])
	java.net.BindException: Address already in use: bind
		at java.base/sun.nio.ch.Net.bind0(Native Method)
		at java.base/sun.nio.ch.Net.bind(Net.java:555)
		at java.base/sun.nio.ch.Net.bind(Net.java:544)
		at java.base/sun.nio.ch.NioSocketImpl.bind(NioSocketImpl.java:648)
		at java.base/java.net.ServerSocket.bind(ServerSocket.java:388)
		at java.base/java.net.ServerSocket.<init>(ServerSocket.java:274)
		at org.apache.catalina.core.StandardServer.await(StandardServer.java:537)
		at org.apache.catalina.startup.Catalina.await(Catalina.java:829)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:777)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 14:18:12.246 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:12.247 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:18:12.261 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:12.262 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:22.421 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Undeploying context [/jv2]
02-Jul-2025 14:18:26.023 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:18:26.025 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:18:26.026 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:18:26.026 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:18:26.026 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:18:26.026 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:18:26.026 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:18:26.026 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:18:26.026 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:18:26.026 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:26.026 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:26.031 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
02-Jul-2025 14:18:26.031 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
02-Jul-2025 14:18:26.032 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
02-Jul-2025 14:18:26.032 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
02-Jul-2025 14:18:26.032 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util=ALL-UNNAMED
02-Jul-2025 14:18:26.033 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
02-Jul-2025 14:18:26.033 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
02-Jul-2025 14:18:26.034 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2\tomcat\conf\logging.properties
02-Jul-2025 14:18:26.035 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:18:26.046 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:18:26.047 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:18:26.047 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dsun.io.useCanonCaches=false
02-Jul-2025 14:18:26.047 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
02-Jul-2025 14:18:26.047 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:26.048 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:26.048 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=C:\PRODUCTS\JViewer2\tomcat\temp
02-Jul-2025 14:18:26.051 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:18:26.051 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:18:26.051 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:18:26.054 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:18:26.196 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:26.208 SEVERE [main] org.apache.catalina.util.LifecycleBase.handleSubClassException Failed to initialize component [Connector["http-nio-8080"]]
	org.apache.catalina.LifecycleException: Protocol handler initialization failed
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1072)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardService.initInternal(StandardService.java:522)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardServer.initInternal(StandardServer.java:986)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:690)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:713)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.apache.catalina.startup.Bootstrap.load(Bootstrap.java:302)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:475)
	Caused by: java.net.BindException: Address already in use: bind
		at java.base/sun.nio.ch.Net.bind0(Native Method)
		at java.base/sun.nio.ch.Net.bind(Net.java:555)
		at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:337)
		at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:294)
		at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:298)
		at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:253)
		at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1465)
		at org.apache.tomcat.util.net.AbstractEndpoint.init(AbstractEndpoint.java:1478)
		at org.apache.coyote.AbstractProtocol.init(AbstractProtocol.java:663)
		at org.apache.coyote.http11.AbstractHttp11Protocol.init(AbstractHttp11Protocol.java:80)
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1070)
		... 13 more
02-Jul-2025 14:18:26.209 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [295] milliseconds
02-Jul-2025 14:18:26.240 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:18:26.241 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:18:26.247 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:18:26.463 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [216] ms
02-Jul-2025 14:18:26.464 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:18:26.696 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [232] ms
02-Jul-2025 14:18:26.697 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:18:26.719 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [23] ms
02-Jul-2025 14:18:26.719 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:18:26.742 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [22] ms
02-Jul-2025 14:18:26.742 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:18:26.762 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [19] ms
02-Jul-2025 14:18:26.766 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [556] milliseconds
02-Jul-2025 14:18:26.770 SEVERE [main] org.apache.catalina.core.StandardServer.await Failed to create server shutdown socket on address [localhost] and port [8005] (base port [8005] and offset [0])
	java.net.BindException: Address already in use: bind
		at java.base/sun.nio.ch.Net.bind0(Native Method)
		at java.base/sun.nio.ch.Net.bind(Net.java:555)
		at java.base/sun.nio.ch.Net.bind(Net.java:544)
		at java.base/sun.nio.ch.NioSocketImpl.bind(NioSocketImpl.java:648)
		at java.base/java.net.ServerSocket.bind(ServerSocket.java:388)
		at java.base/java.net.ServerSocket.<init>(ServerSocket.java:274)
		at org.apache.catalina.core.StandardServer.await(StandardServer.java:537)
		at org.apache.catalina.startup.Catalina.await(Catalina.java:829)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:777)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 14:18:26.772 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:26.773 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:18:26.787 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:26.788 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:30.600 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:18:30.603 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:18:30.604 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:18:30.604 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:18:30.604 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:18:30.604 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:18:30.604 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:18:30.604 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:18:30.605 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:18:30.605 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:30.605 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:30.610 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
02-Jul-2025 14:18:30.610 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
02-Jul-2025 14:18:30.610 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
02-Jul-2025 14:18:30.611 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
02-Jul-2025 14:18:30.611 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util=ALL-UNNAMED
02-Jul-2025 14:18:30.611 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
02-Jul-2025 14:18:30.612 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
02-Jul-2025 14:18:30.613 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2\tomcat\conf\logging.properties
02-Jul-2025 14:18:30.614 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:18:30.614 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:18:30.614 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:18:30.615 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dsun.io.useCanonCaches=false
02-Jul-2025 14:18:30.616 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
02-Jul-2025 14:18:30.616 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:30.616 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:30.617 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=C:\PRODUCTS\JViewer2\tomcat\temp
02-Jul-2025 14:18:30.619 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:18:30.620 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:18:30.620 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:18:30.625 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:18:30.770 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:30.786 SEVERE [main] org.apache.catalina.util.LifecycleBase.handleSubClassException Failed to initialize component [Connector["http-nio-8080"]]
	org.apache.catalina.LifecycleException: Protocol handler initialization failed
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1072)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardService.initInternal(StandardService.java:522)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardServer.initInternal(StandardServer.java:986)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:690)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:713)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.apache.catalina.startup.Bootstrap.load(Bootstrap.java:302)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:475)
	Caused by: java.net.BindException: Address already in use: bind
		at java.base/sun.nio.ch.Net.bind0(Native Method)
		at java.base/sun.nio.ch.Net.bind(Net.java:555)
		at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:337)
		at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:294)
		at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:298)
		at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:253)
		at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1465)
		at org.apache.tomcat.util.net.AbstractEndpoint.init(AbstractEndpoint.java:1478)
		at org.apache.coyote.AbstractProtocol.init(AbstractProtocol.java:663)
		at org.apache.coyote.http11.AbstractHttp11Protocol.init(AbstractHttp11Protocol.java:80)
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1070)
		... 13 more
02-Jul-2025 14:18:30.788 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [297] milliseconds
02-Jul-2025 14:18:30.823 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:18:30.823 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:18:30.830 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:18:31.037 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [207] ms
02-Jul-2025 14:18:31.038 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:18:31.258 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [220] ms
02-Jul-2025 14:18:31.258 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:18:31.282 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [23] ms
02-Jul-2025 14:18:31.282 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:18:31.301 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [19] ms
02-Jul-2025 14:18:31.302 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:18:31.317 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [15] ms
02-Jul-2025 14:18:31.320 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [532] milliseconds
02-Jul-2025 14:18:31.323 SEVERE [main] org.apache.catalina.core.StandardServer.await Failed to create server shutdown socket on address [localhost] and port [8005] (base port [8005] and offset [0])
	java.net.BindException: Address already in use: bind
		at java.base/sun.nio.ch.Net.bind0(Native Method)
		at java.base/sun.nio.ch.Net.bind(Net.java:555)
		at java.base/sun.nio.ch.Net.bind(Net.java:544)
		at java.base/sun.nio.ch.NioSocketImpl.bind(NioSocketImpl.java:648)
		at java.base/java.net.ServerSocket.bind(ServerSocket.java:388)
		at java.base/java.net.ServerSocket.<init>(ServerSocket.java:274)
		at org.apache.catalina.core.StandardServer.await(StandardServer.java:537)
		at org.apache.catalina.startup.Catalina.await(Catalina.java:829)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:777)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 14:18:31.324 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:31.324 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:18:31.337 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:31.339 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:35.164 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:18:35.166 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:18:35.167 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:18:35.167 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:18:35.167 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:18:35.167 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:18:35.167 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:18:35.167 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:18:35.167 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:18:35.167 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:35.168 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:35.173 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
02-Jul-2025 14:18:35.173 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
02-Jul-2025 14:18:35.173 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
02-Jul-2025 14:18:35.173 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
02-Jul-2025 14:18:35.173 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util=ALL-UNNAMED
02-Jul-2025 14:18:35.174 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
02-Jul-2025 14:18:35.174 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
02-Jul-2025 14:18:35.174 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2\tomcat\conf\logging.properties
02-Jul-2025 14:18:35.174 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:18:35.174 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:18:35.175 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:18:35.175 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dsun.io.useCanonCaches=false
02-Jul-2025 14:18:35.175 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
02-Jul-2025 14:18:35.175 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:35.176 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:18:35.177 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=C:\PRODUCTS\JViewer2\tomcat\temp
02-Jul-2025 14:18:35.180 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:18:35.180 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:18:35.180 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:18:35.183 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:18:35.321 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:35.334 SEVERE [main] org.apache.catalina.util.LifecycleBase.handleSubClassException Failed to initialize component [Connector["http-nio-8080"]]
	org.apache.catalina.LifecycleException: Protocol handler initialization failed
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1072)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardService.initInternal(StandardService.java:522)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardServer.initInternal(StandardServer.java:986)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:690)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:713)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.apache.catalina.startup.Bootstrap.load(Bootstrap.java:302)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:475)
	Caused by: java.net.BindException: Address already in use: bind
		at java.base/sun.nio.ch.Net.bind0(Native Method)
		at java.base/sun.nio.ch.Net.bind(Net.java:555)
		at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:337)
		at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:294)
		at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:298)
		at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:253)
		at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1465)
		at org.apache.tomcat.util.net.AbstractEndpoint.init(AbstractEndpoint.java:1478)
		at org.apache.coyote.AbstractProtocol.init(AbstractProtocol.java:663)
		at org.apache.coyote.http11.AbstractHttp11Protocol.init(AbstractHttp11Protocol.java:80)
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1070)
		... 13 more
02-Jul-2025 14:18:35.335 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [270] milliseconds
02-Jul-2025 14:18:35.365 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:18:35.365 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:18:35.370 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:18:35.587 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [216] ms
02-Jul-2025 14:18:35.587 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:18:35.822 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [235] ms
02-Jul-2025 14:18:35.822 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:18:35.845 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [22] ms
02-Jul-2025 14:18:35.845 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:18:35.866 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [20] ms
02-Jul-2025 14:18:35.866 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:18:35.881 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [15] ms
02-Jul-2025 14:18:35.885 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [548] milliseconds
02-Jul-2025 14:18:35.890 SEVERE [main] org.apache.catalina.core.StandardServer.await Failed to create server shutdown socket on address [localhost] and port [8005] (base port [8005] and offset [0])
	java.net.BindException: Address already in use: bind
		at java.base/sun.nio.ch.Net.bind0(Native Method)
		at java.base/sun.nio.ch.Net.bind(Net.java:555)
		at java.base/sun.nio.ch.Net.bind(Net.java:544)
		at java.base/sun.nio.ch.NioSocketImpl.bind(NioSocketImpl.java:648)
		at java.base/java.net.ServerSocket.bind(ServerSocket.java:388)
		at java.base/java.net.ServerSocket.<init>(ServerSocket.java:274)
		at org.apache.catalina.core.StandardServer.await(StandardServer.java:537)
		at org.apache.catalina.startup.Catalina.await(Catalina.java:829)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:777)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 14:18:35.890 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:35.890 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:18:35.904 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:18:35.906 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:19:07.586 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:19:07.588 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:19:07.588 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:19:07.588 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:19:07.588 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:19:07.588 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:19:07.588 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:19:07.589 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:19:07.589 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:19:07.589 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:19:07.589 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:19:07.594 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
02-Jul-2025 14:19:07.595 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
02-Jul-2025 14:19:07.595 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
02-Jul-2025 14:19:07.595 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
02-Jul-2025 14:19:07.595 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util=ALL-UNNAMED
02-Jul-2025 14:19:07.596 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
02-Jul-2025 14:19:07.596 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
02-Jul-2025 14:19:07.597 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2\tomcat\conf\logging.properties
02-Jul-2025 14:19:07.598 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:19:07.598 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:19:07.598 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:19:07.599 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dsun.io.useCanonCaches=false
02-Jul-2025 14:19:07.599 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
02-Jul-2025 14:19:07.600 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:19:07.600 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:19:07.600 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=C:\PRODUCTS\JViewer2\tomcat\temp
02-Jul-2025 14:19:07.603 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:19:07.604 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:19:07.604 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:19:07.608 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:19:07.743 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:19:07.760 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [283] milliseconds
02-Jul-2025 14:19:07.791 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:19:07.792 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:19:07.800 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:19:08.016 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [215] ms
02-Jul-2025 14:19:08.016 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:19:08.251 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [235] ms
02-Jul-2025 14:19:08.251 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:19:08.272 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [21] ms
02-Jul-2025 14:19:08.273 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:19:08.294 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [21] ms
02-Jul-2025 14:19:08.295 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:19:08.311 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [16] ms
02-Jul-2025 14:19:08.315 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:19:08.363 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [602] milliseconds
02-Jul-2025 14:20:08.339 INFO [Catalina-utility-2] org.apache.catalina.users.MemoryUserDatabase.backgroundProcess Reloading memory user database [UserDatabase] from updated source [file:/C:/PRODUCTS/JViewer2/tomcat/conf/tomcat-users.xml]
02-Jul-2025 14:20:20.785 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:20:20.788 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:20:20.788 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:20:20.788 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:20:20.789 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:20:20.789 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:20:20.789 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:20:20.789 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:20:20.789 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:20:20.789 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:20:20.789 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:20:20.795 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
02-Jul-2025 14:20:20.795 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
02-Jul-2025 14:20:20.795 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
02-Jul-2025 14:20:20.795 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
02-Jul-2025 14:20:20.796 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util=ALL-UNNAMED
02-Jul-2025 14:20:20.796 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
02-Jul-2025 14:20:20.796 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
02-Jul-2025 14:20:20.796 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2\tomcat\conf\logging.properties
02-Jul-2025 14:20:20.796 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:20:20.798 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:20:20.798 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:20:20.798 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dsun.io.useCanonCaches=false
02-Jul-2025 14:20:20.798 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
02-Jul-2025 14:20:20.798 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:20:20.799 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:20:20.799 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=C:\PRODUCTS\JViewer2\tomcat\temp
02-Jul-2025 14:20:20.802 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:20:20.802 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:20:20.802 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:20:20.806 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:20:20.965 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:20:20.982 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [307] milliseconds
02-Jul-2025 14:20:21.012 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:20:21.012 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:20:21.019 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:20:21.244 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [226] ms
02-Jul-2025 14:20:21.245 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:20:21.490 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [245] ms
02-Jul-2025 14:20:21.490 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:20:21.514 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [24] ms
02-Jul-2025 14:20:21.514 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:20:21.539 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [24] ms
02-Jul-2025 14:20:21.539 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:20:21.559 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [20] ms
02-Jul-2025 14:20:21.562 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:20:21.613 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [630] milliseconds
02-Jul-2025 14:42:29.613 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:42:29.615 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:42:29.615 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:42:29.615 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:42:29.615 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:42:29.615 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:42:29.616 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:42:29.616 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:42:29.616 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:42:29.616 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:42:29.616 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:42:29.624 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=localhost:12202
02-Jul-2025 14:42:29.624 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=c:\PRODUCTS\JViewer2\tomcat/conf/logging.properties
02-Jul-2025 14:42:29.624 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:42:29.624 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:42:29.624 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:42:29.624 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
02-Jul-2025 14:42:29.624 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:42:29.624 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:42:29.624 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=c:\PRODUCTS\JViewer2\tomcat/temp
02-Jul-2025 14:42:29.628 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:42:29.628 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:42:29.628 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:42:29.632 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:42:29.787 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:42:29.804 SEVERE [main] org.apache.catalina.util.LifecycleBase.handleSubClassException Failed to initialize component [Connector["http-nio-8080"]]
	org.apache.catalina.LifecycleException: Protocol handler initialization failed
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1072)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardService.initInternal(StandardService.java:522)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardServer.initInternal(StandardServer.java:986)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:690)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:713)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.apache.catalina.startup.Bootstrap.load(Bootstrap.java:302)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:475)
	Caused by: java.net.BindException: Address already in use: bind
		at java.base/sun.nio.ch.Net.bind0(Native Method)
		at java.base/sun.nio.ch.Net.bind(Net.java:555)
		at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:337)
		at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:294)
		at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:298)
		at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:253)
		at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1465)
		at org.apache.tomcat.util.net.AbstractEndpoint.init(AbstractEndpoint.java:1478)
		at org.apache.coyote.AbstractProtocol.init(AbstractProtocol.java:663)
		at org.apache.coyote.http11.AbstractHttp11Protocol.init(AbstractHttp11Protocol.java:80)
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1070)
		... 13 more
02-Jul-2025 14:42:29.804 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [294] milliseconds
02-Jul-2025 14:42:29.841 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:42:29.841 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:42:29.849 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:42:30.079 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [230] ms
02-Jul-2025 14:42:30.079 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:42:30.325 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [246] ms
02-Jul-2025 14:42:30.325 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:42:30.348 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [23] ms
02-Jul-2025 14:42:30.348 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:42:30.371 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [23] ms
02-Jul-2025 14:42:30.371 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:42:30.390 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [19] ms
02-Jul-2025 14:42:30.394 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [589] milliseconds
02-Jul-2025 14:42:30.397 SEVERE [main] org.apache.catalina.core.StandardServer.await Failed to create server shutdown socket on address [localhost] and port [8005] (base port [8005] and offset [0])
	java.net.BindException: Address already in use: bind
		at java.base/sun.nio.ch.Net.bind0(Native Method)
		at java.base/sun.nio.ch.Net.bind(Net.java:555)
		at java.base/sun.nio.ch.Net.bind(Net.java:544)
		at java.base/sun.nio.ch.NioSocketImpl.bind(NioSocketImpl.java:648)
		at java.base/java.net.ServerSocket.bind(ServerSocket.java:388)
		at java.base/java.net.ServerSocket.<init>(ServerSocket.java:274)
		at org.apache.catalina.core.StandardServer.await(StandardServer.java:537)
		at org.apache.catalina.startup.Catalina.await(Catalina.java:829)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:777)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 14:42:30.398 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:42:30.398 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:42:30.405 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:42:30.405 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:42:30.406 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:42:30.412 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:42:30.412 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:42:30.413 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:42:30.415 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:42:30.415 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:42:30.415 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:42:30.417 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:42:30.417 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:42:30.417 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:42:30.421 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:42:30.421 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:42:30.421 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:42:30.422 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:42:30.424 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:42:47.518 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:42:47.520 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:42:47.521 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:42:47.521 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:42:47.521 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:42:47.521 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:42:47.521 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:42:47.521 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:42:47.521 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:42:47.521 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:42:47.522 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:42:47.534 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=localhost:12218
02-Jul-2025 14:42:47.534 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=c:\PRODUCTS\JViewer2\tomcat/conf/logging.properties
02-Jul-2025 14:42:47.534 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:42:47.534 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:42:47.534 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:42:47.535 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
02-Jul-2025 14:42:47.535 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:42:47.535 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:42:47.535 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=c:\PRODUCTS\JViewer2\tomcat/temp
02-Jul-2025 14:42:47.538 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:42:47.539 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:42:47.539 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:42:47.544 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:42:47.691 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:42:47.714 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [308] milliseconds
02-Jul-2025 14:42:47.757 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:42:47.757 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:42:47.765 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:42:48.006 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [241] ms
02-Jul-2025 14:42:48.007 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:42:48.236 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [229] ms
02-Jul-2025 14:42:48.236 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:42:48.259 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [22] ms
02-Jul-2025 14:42:48.259 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:42:48.282 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [23] ms
02-Jul-2025 14:42:48.283 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:42:48.298 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [15] ms
02-Jul-2025 14:42:48.302 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:42:48.354 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [639] milliseconds
02-Jul-2025 14:44:14.924 INFO [main] org.apache.catalina.core.StandardServer.await A valid shutdown command was received via the shutdown port. Stopping the Server instance.
02-Jul-2025 14:44:14.924 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:44:15.044 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:44:15.048 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:44:15.048 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:44:15.049 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:44:15.052 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:44:15.052 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:44:15.052 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:44:15.053 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:44:15.053 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:44:15.054 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:44:15.062 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:44:15.062 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:44:15.062 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:44:15.062 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:44:15.063 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:44:15.063 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:44:15.064 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:44:15.075 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:44:44.706 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:44:44.709 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:44:44.709 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:44:44.709 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:44:44.709 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:44:44.709 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:44:44.709 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:44:44.709 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:44:44.709 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:44:44.709 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:44:44.709 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:44:44.718 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=c:\PRODUCTS\JViewer2\tomcat/conf/logging.properties
02-Jul-2025 14:44:44.718 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:44:44.718 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:44:44.718 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:44:44.718 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
02-Jul-2025 14:44:44.718 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:44:44.718 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:44:44.718 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=c:\PRODUCTS\JViewer2\tomcat/temp
02-Jul-2025 14:44:44.721 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:44:44.722 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:44:44.722 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:44:44.725 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:44:44.871 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:44:44.888 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [278] milliseconds
02-Jul-2025 14:44:44.923 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:44:44.924 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:44:44.930 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:44:45.142 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [212] ms
02-Jul-2025 14:44:45.142 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:44:45.372 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [230] ms
02-Jul-2025 14:44:45.372 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:44:45.393 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [21] ms
02-Jul-2025 14:44:45.393 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:44:45.431 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [37] ms
02-Jul-2025 14:44:45.431 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:44:45.447 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [16] ms
02-Jul-2025 14:44:45.451 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:44:45.505 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [616] milliseconds
02-Jul-2025 14:45:15.481 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\JViewer2-0.6.0-SNAPSHOT.war]
02-Jul-2025 14:45:15.654 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\JViewer2-0.6.0-SNAPSHOT.war] has finished in [173] ms
02-Jul-2025 14:45:35.747 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Undeploying context [/JViewer2-0.6.0-SNAPSHOT]
02-Jul-2025 14:45:35.751 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:45:35.754 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:45:35.755 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:45:36.089 INFO [main] org.apache.catalina.core.StandardServer.await A valid shutdown command was received via the shutdown port. Stopping the Server instance.
02-Jul-2025 14:45:36.090 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:45:36.227 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:45:36.229 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:45:36.229 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:45:36.230 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:45:36.233 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:45:36.233 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:45:36.233 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:45:36.235 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:45:36.235 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:45:36.235 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:45:36.239 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:45:36.240 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:45:36.240 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:45:36.243 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:45:36.243 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:45:36.243 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:45:36.243 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:45:36.249 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:47:21.929 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:47:21.932 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:47:21.932 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:47:21.932 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:47:21.932 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:47:21.932 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:47:21.932 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:47:21.932 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:47:21.932 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:47:21.932 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:47:21.932 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:47:21.940 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=localhost:12362
02-Jul-2025 14:47:21.940 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=c:\PRODUCTS\JViewer2\tomcat/conf/logging.properties
02-Jul-2025 14:47:21.940 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:47:21.940 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:47:21.940 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:47:21.940 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
02-Jul-2025 14:47:21.940 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:47:21.941 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:47:21.941 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=c:\PRODUCTS\JViewer2\tomcat/temp
02-Jul-2025 14:47:21.943 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:47:21.944 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:47:21.944 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:47:21.948 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:47:22.105 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:47:22.124 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [305] milliseconds
02-Jul-2025 14:47:22.157 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:47:22.157 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:47:22.184 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war]
02-Jul-2025 14:47:22.512 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war] has finished in [328] ms
02-Jul-2025 14:47:22.513 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:47:22.537 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [24] ms
02-Jul-2025 14:47:22.537 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:47:22.761 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [224] ms
02-Jul-2025 14:47:22.761 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:47:22.788 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [27] ms
02-Jul-2025 14:47:22.789 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:47:22.827 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [39] ms
02-Jul-2025 14:47:22.827 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:47:22.845 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [17] ms
02-Jul-2025 14:47:22.848 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:47:22.904 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [779] milliseconds
02-Jul-2025 14:49:01.022 INFO [main] org.apache.catalina.core.StandardServer.await A valid shutdown command was received via the shutdown port. Stopping the Server instance.
02-Jul-2025 14:49:01.022 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:49:01.134 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:49:01.138 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:49:01.139 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:49:01.139 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:49:01.141 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:49:01.141 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:49:01.141 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:49:01.142 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:49:01.143 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:49:01.143 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:49:01.145 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:49:01.145 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:49:01.145 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:49:01.148 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:49:01.148 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:49:01.149 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:49:01.151 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:49:01.152 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:49:01.152 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:49:01.153 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:49:01.168 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:49:38.191 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:49:38.194 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:49:38.194 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:49:38.194 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:49:38.194 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:49:38.194 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:49:38.194 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:49:38.194 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:49:38.194 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:49:38.194 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:49:38.194 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:49:38.201 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=localhost:12430
02-Jul-2025 14:49:38.201 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=c:\PRODUCTS\JViewer2\tomcat/conf/logging.properties
02-Jul-2025 14:49:38.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:49:38.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:49:38.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:49:38.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
02-Jul-2025 14:49:38.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:49:38.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:49:38.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=c:\PRODUCTS\JViewer2\tomcat/temp
02-Jul-2025 14:49:38.206 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:49:38.206 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:49:38.206 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:49:38.210 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:49:38.370 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:49:38.391 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [309] milliseconds
02-Jul-2025 14:49:38.423 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:49:38.423 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:49:38.448 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war]
02-Jul-2025 14:49:38.696 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war] has finished in [248] ms
02-Jul-2025 14:49:38.696 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:49:38.729 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [33] ms
02-Jul-2025 14:49:38.729 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:49:38.954 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [225] ms
02-Jul-2025 14:49:38.955 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:49:38.976 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [22] ms
02-Jul-2025 14:49:38.977 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:49:39.012 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [36] ms
02-Jul-2025 14:49:39.013 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:49:39.030 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [17] ms
02-Jul-2025 14:49:39.034 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:49:39.082 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [689] milliseconds
02-Jul-2025 14:52:19.922 INFO [main] org.apache.catalina.core.StandardServer.await A valid shutdown command was received via the shutdown port. Stopping the Server instance.
02-Jul-2025 14:52:19.923 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:52:20.051 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:52:20.056 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:52:20.056 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:52:20.056 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:52:20.058 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:52:20.058 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:52:20.058 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:52:20.059 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:52:20.060 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:52:20.060 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:52:20.062 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:52:20.062 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:52:20.062 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:52:20.066 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:52:20.066 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:52:20.066 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:52:20.068 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:52:20.069 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:52:20.069 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:52:20.069 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:52:20.082 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:55:26.390 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:55:26.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:55:26.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:55:26.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:55:26.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:55:26.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:55:26.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:55:26.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:55:26.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:55:26.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:55:26.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:55:26.401 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=localhost:13019
02-Jul-2025 14:55:26.401 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=c:\PRODUCTS\JViewer2\tomcat/conf/logging.properties
02-Jul-2025 14:55:26.401 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:55:26.401 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:55:26.401 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:55:26.401 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
02-Jul-2025 14:55:26.401 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:55:26.401 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:55:26.402 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=c:\PRODUCTS\JViewer2\tomcat/temp
02-Jul-2025 14:55:26.405 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:55:26.405 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:55:26.405 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:55:26.408 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:55:26.558 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:55:26.576 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [291] milliseconds
02-Jul-2025 14:55:26.610 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:55:26.610 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:55:26.638 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war]
02-Jul-2025 14:55:26.651 INFO [main] org.apache.catalina.startup.ExpandWar.expand An expanded directory [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2] was found with a last modified time that did not match the associated WAR. It will be deleted.
02-Jul-2025 14:55:26.968 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war] has finished in [330] ms
02-Jul-2025 14:55:26.969 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:55:26.997 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [28] ms
02-Jul-2025 14:55:26.997 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:55:27.216 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [218] ms
02-Jul-2025 14:55:27.216 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:55:27.237 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [21] ms
02-Jul-2025 14:55:27.238 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:55:27.272 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [35] ms
02-Jul-2025 14:55:27.272 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:55:27.288 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [16] ms
02-Jul-2025 14:55:27.292 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:55:27.342 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [765] milliseconds
02-Jul-2025 14:56:11.336 INFO [main] org.apache.catalina.core.StandardServer.await A valid shutdown command was received via the shutdown port. Stopping the Server instance.
02-Jul-2025 14:56:11.336 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:56:11.447 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:56:11.456 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:56:11.457 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:56:11.458 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:56:11.462 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:56:11.463 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:56:11.463 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:56:11.466 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:56:11.466 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:56:11.467 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:56:11.471 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:56:11.471 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:56:11.471 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:56:11.476 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:56:11.476 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:56:11.476 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:56:11.477 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:56:11.477 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:56:11.478 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:56:11.478 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:56:11.480 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:56:28.761 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:56:28.764 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:56:28.764 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:56:28.764 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:56:28.764 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:56:28.764 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:56:28.764 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:56:28.764 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:56:28.765 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:56:28.765 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:56:28.765 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:56:28.775 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=localhost:13091
02-Jul-2025 14:56:28.776 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=c:\PRODUCTS\JViewer2\tomcat/conf/logging.properties
02-Jul-2025 14:56:28.776 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:56:28.776 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:56:28.776 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:56:28.776 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
02-Jul-2025 14:56:28.776 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:56:28.776 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:56:28.776 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=c:\PRODUCTS\JViewer2\tomcat/temp
02-Jul-2025 14:56:28.780 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:56:28.780 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:56:28.780 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:56:28.785 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:56:28.951 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:56:28.972 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [316] milliseconds
02-Jul-2025 14:56:29.000 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:56:29.000 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:56:29.010 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war]
02-Jul-2025 14:56:29.232 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war] has finished in [221] ms
02-Jul-2025 14:56:29.232 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:56:29.263 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [31] ms
02-Jul-2025 14:56:29.263 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:56:29.489 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [225] ms
02-Jul-2025 14:56:29.489 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:56:29.518 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [29] ms
02-Jul-2025 14:56:29.519 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:56:29.559 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [41] ms
02-Jul-2025 14:56:29.559 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:56:29.580 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [21] ms
02-Jul-2025 14:56:29.584 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:56:29.633 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [661] milliseconds
02-Jul-2025 14:57:02.305 INFO [main] org.apache.catalina.core.StandardServer.await A valid shutdown command was received via the shutdown port. Stopping the Server instance.
02-Jul-2025 14:57:02.305 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:57:02.422 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:57:02.428 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:57:02.428 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:57:02.429 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:57:02.431 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:57:02.432 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:57:02.432 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:57:02.433 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:57:02.434 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:57:02.434 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:57:02.436 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:57:02.436 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:57:02.436 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:57:02.442 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:57:02.442 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:57:02.442 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:57:02.444 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:57:02.444 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:57:02.444 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:57:02.445 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:57:02.455 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:57:08.380 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 14:57:08.383 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 14:57:08.383 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 14:57:08.383 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 14:57:08.383 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 14:57:08.383 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 14:57:08.383 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 14:57:08.383 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 14:57:08.383 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 14:57:08.383 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:57:08.383 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:57:08.391 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=localhost:13187
02-Jul-2025 14:57:08.391 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=c:\PRODUCTS\JViewer2\tomcat/conf/logging.properties
02-Jul-2025 14:57:08.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 14:57:08.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 14:57:08.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 14:57:08.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
02-Jul-2025 14:57:08.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:57:08.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 14:57:08.392 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=c:\PRODUCTS\JViewer2\tomcat/temp
02-Jul-2025 14:57:08.395 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 14:57:08.395 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 14:57:08.395 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 14:57:08.399 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 14:57:08.556 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:57:08.577 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [304] milliseconds
02-Jul-2025 14:57:08.620 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 14:57:08.620 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 14:57:08.636 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war]
02-Jul-2025 14:57:08.900 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war] has finished in [264] ms
02-Jul-2025 14:57:08.901 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 14:57:08.931 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [29] ms
02-Jul-2025 14:57:08.931 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 14:57:09.142 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [211] ms
02-Jul-2025 14:57:09.142 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 14:57:09.168 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [26] ms
02-Jul-2025 14:57:09.168 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 14:57:09.199 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [31] ms
02-Jul-2025 14:57:09.199 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 14:57:09.215 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [16] ms
02-Jul-2025 14:57:09.219 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:57:09.265 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [687] milliseconds
02-Jul-2025 14:59:27.482 INFO [main] org.apache.catalina.core.StandardServer.await A valid shutdown command was received via the shutdown port. Stopping the Server instance.
02-Jul-2025 14:59:27.483 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:59:27.588 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 14:59:27.593 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:59:27.593 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:59:27.594 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:59:27.596 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:59:27.596 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:59:27.596 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:59:27.599 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:59:27.599 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:59:27.599 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:59:27.601 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:59:27.602 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:59:27.602 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:59:27.607 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:59:27.607 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:59:27.607 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:59:27.609 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 14:59:27.610 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 14:59:27.610 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 14:59:27.610 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 14:59:27.616 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 15:00:24.480 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 15:00:24.483 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 15:00:24.483 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 15:00:24.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 15:00:24.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 15:00:24.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 15:00:24.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 15:00:24.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 15:00:24.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 15:00:24.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 15:00:24.484 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 15:00:24.494 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=localhost:13261
02-Jul-2025 15:00:24.494 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=c:\PRODUCTS\JViewer2\tomcat/conf/logging.properties
02-Jul-2025 15:00:24.494 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 15:00:24.494 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 15:00:24.494 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 15:00:24.494 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
02-Jul-2025 15:00:24.494 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 15:00:24.495 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 15:00:24.495 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=c:\PRODUCTS\JViewer2\tomcat/temp
02-Jul-2025 15:00:24.498 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 15:00:24.498 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 15:00:24.498 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 15:00:24.502 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 15:00:24.667 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 15:00:24.688 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [311] milliseconds
02-Jul-2025 15:00:24.724 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 15:00:24.725 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 15:00:24.736 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war]
02-Jul-2025 15:00:24.986 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war] has finished in [250] ms
02-Jul-2025 15:00:24.987 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 15:00:25.016 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [29] ms
02-Jul-2025 15:00:25.016 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 15:00:25.239 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [223] ms
02-Jul-2025 15:00:25.239 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 15:00:25.266 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [27] ms
02-Jul-2025 15:00:25.267 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 15:00:25.298 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [31] ms
02-Jul-2025 15:00:25.298 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 15:00:25.317 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [19] ms
02-Jul-2025 15:00:25.322 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 15:00:25.371 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [682] milliseconds
02-Jul-2025 15:01:10.388 INFO [http-nio-8080-exec-8] com.untill.ProxyServlet.init Using web.xml configuration - Host: http://*************, Port: 6116
02-Jul-2025 15:01:10.404 INFO [http-nio-8080-exec-8] com.untill.ProxyServlet.init ProxyServlet initialized with target: http://*************:6116
02-Jul-2025 15:01:30.728 INFO [http-nio-8080-exec-8] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/something
02-Jul-2025 15:01:34.167 SEVERE [http-nio-8080-exec-8] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:01:46.230 INFO [http-nio-8080-exec-10] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/something
02-Jul-2025 15:01:48.486 SEVERE [http-nio-8080-exec-10] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:01:57.727 INFO [http-nio-8080-exec-2] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy
02-Jul-2025 15:01:57.728 SEVERE [http-nio-8080-exec-2] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:02:11.272 INFO [http-nio-8080-exec-1] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/dfg
02-Jul-2025 15:02:13.528 SEVERE [http-nio-8080-exec-1] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:02:34.561 INFO [http-nio-8080-exec-6] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/dfg
02-Jul-2025 15:02:36.819 SEVERE [http-nio-8080-exec-6] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:04:18.027 INFO [http-nio-8080-exec-4] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/dfg
02-Jul-2025 15:04:20.286 SEVERE [http-nio-8080-exec-4] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:04:56.778 INFO [main] org.apache.catalina.core.StandardServer.await A valid shutdown command was received via the shutdown port. Stopping the Server instance.
02-Jul-2025 15:04:56.778 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 15:04:56.897 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
02-Jul-2025 15:04:56.901 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 15:04:56.902 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 15:04:56.902 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 15:04:56.904 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 15:04:56.905 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 15:04:56.905 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 15:04:56.906 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 15:04:56.906 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 15:04:56.907 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 15:04:56.910 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 15:04:56.910 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 15:04:56.910 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 15:04:56.914 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 15:04:56.915 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 15:04:56.915 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 15:04:56.917 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesObjectStreamClassCaches When running on Java 9 or later you need to add "--add-opens=java.base/java.io=ALL-UNNAMED" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
02-Jul-2025 15:04:56.917 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks When running on Java 9 or later you need to add "--add-opens=java.base/java.lang=ALL-UNNAMED" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
02-Jul-2025 15:04:56.917 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesRmiTargets When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
02-Jul-2025 15:04:56.918 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
02-Jul-2025 15:04:56.929 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
02-Jul-2025 15:05:00.981 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 15:05:00.984 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 15:05:00.984 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 15:05:00.984 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 15:05:00.984 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 15:05:00.984 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 15:05:00.984 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\BellSoft\LibericaJDK-17-Full
02-Jul-2025 15:05:00.984 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           17.0.13+12-LTS
02-Jul-2025 15:05:00.984 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            BellSoft
02-Jul-2025 15:05:00.984 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 15:05:00.984 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 15:05:00.993 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=localhost:13728
02-Jul-2025 15:05:00.994 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=c:\PRODUCTS\JViewer2\tomcat/conf/logging.properties
02-Jul-2025 15:05:00.994 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 15:05:00.994 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
02-Jul-2025 15:05:00.994 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
02-Jul-2025 15:05:00.994 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
02-Jul-2025 15:05:00.994 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 15:05:00.994 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=c:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 15:05:00.995 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=c:\PRODUCTS\JViewer2\tomcat/temp
02-Jul-2025 15:05:00.998 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 15:05:00.999 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 15:05:00.999 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 15:05:01.003 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 15:05:01.155 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 15:05:01.175 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [305] milliseconds
02-Jul-2025 15:05:01.203 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 15:05:01.203 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 15:05:01.215 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war]
02-Jul-2025 15:05:01.452 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [C:\PRODUCTS\JViewer2\tomcat\webapps\jv2.war] has finished in [236] ms
02-Jul-2025 15:05:01.452 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 15:05:01.480 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [28] ms
02-Jul-2025 15:05:01.481 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 15:05:01.707 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [226] ms
02-Jul-2025 15:05:01.708 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 15:05:01.731 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [22] ms
02-Jul-2025 15:05:01.731 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 15:05:01.763 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [32] ms
02-Jul-2025 15:05:01.764 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 15:05:01.781 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [17] ms
02-Jul-2025 15:05:01.785 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 15:05:01.838 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [663] milliseconds
02-Jul-2025 15:07:37.589 INFO [http-nio-8080-exec-4] com.untill.ProxyServlet.init Using web.xml configuration - Host: http://*************, Port: 6116
02-Jul-2025 15:07:43.506 INFO [http-nio-8080-exec-4] com.untill.ProxyServlet.init ProxyServlet initialized with target: http://*************:6116
02-Jul-2025 15:07:48.463 INFO [http-nio-8080-exec-4] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/ddd
02-Jul-2025 15:07:50.723 SEVERE [http-nio-8080-exec-4] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:08:38.944 INFO [http-nio-8080-exec-2] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/ddd
02-Jul-2025 15:09:16.336 SEVERE [http-nio-8080-exec-2] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:10:40.713 INFO [http-nio-8080-exec-6] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/ddd
02-Jul-2025 15:11:01.538 SEVERE [http-nio-8080-exec-6] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:13:34.495 INFO [http-nio-8080-exec-6] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/ddd
02-Jul-2025 15:13:35.887 INFO [http-nio-8080-exec-2] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/ddd
02-Jul-2025 15:13:36.751 SEVERE [http-nio-8080-exec-6] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:13:36.751 SEVERE [http-nio-8080-exec-2] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:15:27.213 INFO [http-nio-8080-exec-6] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/ddd
02-Jul-2025 15:15:29.470 SEVERE [http-nio-8080-exec-6] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:18:00.838 INFO [http-nio-8080-exec-5] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/ddd
02-Jul-2025 15:18:03.092 SEVERE [http-nio-8080-exec-5] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:18:18.583 INFO [http-nio-8080-exec-1] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/ddd
02-Jul-2025 15:18:20.838 SEVERE [http-nio-8080-exec-1] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
02-Jul-2025 15:18:25.567 INFO [http-nio-8080-exec-7] com.untill.ProxyServlet.service Proxying GET request to: http://http://*************:6116/jv2/proxy/ddd
02-Jul-2025 15:18:25.575 SEVERE [http-nio-8080-exec-7] com.untill.ProxyServlet.service Error while forwarding response
	java.net.UnknownHostException: http
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at java.base/java.net.Socket.connect(Socket.java:583)
		at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:183)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)
		at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)
		at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)
		at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1323)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1256)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1142)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1071)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1701)
		at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1625)
		at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
		at com.untill.ProxyServlet.service(ProxyServlet.java:108)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:656)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
