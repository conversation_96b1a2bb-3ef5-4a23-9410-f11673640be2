02-Jul-2025 13:15:10.637 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\Java\jdk1.8.0_211\jre
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_211-b12
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Oracle Corporation
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:15:10.639 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xrunjdwp:transport=dt_socket,address=localhost:9785,server=n,suspend=y
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2/tomcat/conf/logging.properties
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Duntill.home=C:\PRODUCTS\JViewer2/test-config
02-Jul-2025 13:15:10.642 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dfile.encoding=UTF-8
02-Jul-2025 13:15:10.645 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 13:15:10.645 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 13:15:10.645 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 13:15:10.650 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 13:15:10.829 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:15:10.839 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [317] milliseconds
02-Jul-2025 13:15:10.858 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 13:15:10.858 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 13:15:10.865 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 13:15:10.897 WARNING [main] org.apache.catalina.webresources.DirResourceSet.initInternal Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] which is part of the web application [/docs]
02-Jul-2025 13:15:10.964 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.026 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.195 WARNING [main] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [147] milliseconds.
02-Jul-2025 13:15:11.210 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [345] ms
02-Jul-2025 13:15:11.210 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 13:15:11.228 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.372 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.404 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [194] ms
02-Jul-2025 13:15:11.404 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 13:15:11.414 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.422 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.428 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [24] ms
02-Jul-2025 13:15:11.428 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 13:15:11.440 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.455 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.457 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [28] ms
02-Jul-2025 13:15:11.457 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 13:15:11.466 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.474 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:15:11.476 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [19] ms
02-Jul-2025 13:15:11.480 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:15:11.492 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [652] milliseconds
02-Jul-2025 13:27:37.003 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.106
02-Jul-2025 13:27:37.005 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 5 2025 19:02:30 UTC
02-Jul-2025 13:27:37.005 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: *********
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Windows 10
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            10.0
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             C:\Program Files\Java\jdk1.8.0_211\jre
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_211-b12
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Oracle Corporation
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:27:37.006 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         C:\PRODUCTS\JViewer2\tomcat
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xrunjdwp:transport=dt_socket,address=localhost:10228,server=n,suspend=y
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=C:\PRODUCTS\JViewer2/tomcat
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=C:\PRODUCTS\JViewer2/tomcat/conf/logging.properties
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Duntill.home=C:\PRODUCTS\JViewer2/test-config
02-Jul-2025 13:27:37.007 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dfile.encoding=UTF-8
02-Jul-2025 13:27:37.009 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
02-Jul-2025 13:27:37.009 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
02-Jul-2025 13:27:37.009 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
02-Jul-2025 13:27:37.013 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
02-Jul-2025 13:27:37.190 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:27:37.202 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [306] milliseconds
02-Jul-2025 13:27:37.219 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
02-Jul-2025 13:27:37.219 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.106]
02-Jul-2025 13:27:37.226 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs]
02-Jul-2025 13:27:37.258 WARNING [main] org.apache.catalina.webresources.DirResourceSet.initInternal Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] which is part of the web application [/docs]
02-Jul-2025 13:27:37.314 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.355 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.530 WARNING [main] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [152] milliseconds.
02-Jul-2025 13:27:37.543 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\docs] has finished in [317] ms
02-Jul-2025 13:27:37.543 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples]
02-Jul-2025 13:27:37.561 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.700 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.733 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\examples] has finished in [190] ms
02-Jul-2025 13:27:37.733 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager]
02-Jul-2025 13:27:37.742 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.750 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.755 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\host-manager] has finished in [22] ms
02-Jul-2025 13:27:37.755 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager]
02-Jul-2025 13:27:37.767 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.776 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.778 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\manager] has finished in [23] ms
02-Jul-2025 13:27:37.778 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT]
02-Jul-2025 13:27:37.788 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.catalina.startup.ContextConfig.processJarsForWebFragments(ContextConfig.java:2048)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1259)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:972)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:290)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4407)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.803 WARNING [main] org.apache.tomcat.util.scan.StandardJarScanner.processURLs Failed to scan [file:/c:/PRODUCTS/JViewer2/tomcat/lib/*.jar] from classloader hierarchy
	java.io.FileNotFoundException: c:\PRODUCTS\JViewer2\tomcat\lib\*.jar (The filename, directory name, or volume label syntax is incorrect)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:316)
		at org.apache.tomcat.util.scan.JarFileUrlJar.<init>(JarFileUrlJar.java:64)
		at org.apache.tomcat.util.scan.JarFactory.newInstance(JarFactory.java:49)
		at org.apache.tomcat.util.scan.StandardJarScanner.process(StandardJarScanner.java:381)
		at org.apache.tomcat.util.scan.StandardJarScanner.processURLs(StandardJarScanner.java:322)
		at org.apache.tomcat.util.scan.StandardJarScanner.doScanClassPath(StandardJarScanner.java:277)
		at org.apache.tomcat.util.scan.StandardJarScanner.scan(StandardJarScanner.java:241)
		at org.apache.jasper.servlet.TldScanner.scanJars(TldScanner.java:248)
		at org.apache.jasper.servlet.TldScanner.scan(TldScanner.java:98)
		at org.apache.jasper.servlet.JasperInitializer.onStartup(JasperInitializer.java:81)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4491)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1174)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1883)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployDirectories(HostConfig.java:1085)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:470)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1579)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:336)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:76)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:412)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:739)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476)
02-Jul-2025 13:27:37.809 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [C:\PRODUCTS\JViewer2\tomcat\webapps\ROOT] has finished in [31] ms
02-Jul-2025 13:27:37.821 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
02-Jul-2025 13:27:37.844 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [641] milliseconds
