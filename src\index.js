import React from 'react';
import ReactDOM from 'react-dom';
import './index.css';
import reducers from './reducers';
import App from './App';
import ReduxThunk from 'redux-thunk';
import { Provider } from 'react-redux'
import { createStore, applyMiddleware } from 'redux'
import { CookiesProvider } from 'react-cookie';

import registerServiceWorker from './registerServiceWorker';

const store = createStore(reducers, {}, applyMiddleware(ReduxThunk))

ReactDOM.render(
    <Provider store={store}>
        <CookiesProvider>
            <App />
        </CookiesProvider>
    </Provider>, 
    document.getElementById('root')
);
registerServiceWorker();
