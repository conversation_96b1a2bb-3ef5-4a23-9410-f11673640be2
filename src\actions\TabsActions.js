import _ from 'lodash';
import { ACTION_SWITCH_TAB, ACTION_NEW_TAB, ACTION_ADD_TAB_FILTER } from "./Types";

export const actionSwitchTab = (sessionId) => ({
    type: ACTION_SWITCH_TAB,
    payload: { sessionId }
});

export const actionNewTab = () => ({
    type: ACTION_NEW_TAB
});

export const addTabFilter = (sessionId, value) => {
    let filters = _.isArray(value) ? value : [value]; 

    return {
        type: ACTION_ADD_TAB_FILTER,
        payload: { sessionId, filters }, 
    };
};