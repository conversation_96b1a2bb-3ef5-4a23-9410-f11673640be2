package com.untill.auth;

/**
 * Represents a user from the UNTILL_USERS table
 */
public class User {
    private final String name;
    private final String password;
    private final boolean isActive;
    private final boolean hasJlogView;

    public User(String name, String password, boolean isActive, boolean hasJlogView) {
        this.name = name;
        this.password = password;
        this.isActive = isActive;
        this.hasJlogView = hasJlogView;
    }

    public String getName() {
        return name;
    }

    public String getPassword() {
        return password;
    }

    public boolean isActive() {
        return isActive;
    }

    public boolean hasJlogView() {
        return hasJlogView;
    }

    /**
     * Check if user is valid for JLOG access
     */
    public boolean isValidForJlog() {
        return isActive && hasJlogView;
    }

    @Override
    public String toString() {
        return "User{" +
                "name='" + name + '\'' +
                ", isActive=" + isActive +
                ", hasJlogView=" + hasJlogView +
                '}';
    }
}
