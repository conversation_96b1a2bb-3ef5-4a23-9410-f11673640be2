<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 Configuration Reference (9.0.106) - The Loader Component</title><meta name="author" content="<PERSON>"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9 Configuration Reference</h1><div class="versionInfo">
            Version 9.0.106,
            <time datetime="2025-06-05">Jun 5 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="runtime-attributes.html">Runtime attributes</a></li><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">JASPIC</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>The Loader Component</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Attributes">Attributes</a><ol><li><a href="#Common_Attributes">Common Attributes</a></li><li><a href="#Standard_Implementation">Standard Implementation</a></li></ol></li><li><a href="#Nested_Components">Nested Components</a></li><li><a href="#Special_Features">Special Features</a><ol><li><a href="#Logging">Logging</a></li></ol></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

  <p>The <strong>Loader</strong> element represents the <em>web
  application class loader</em> that will be used to load Java
  classes and resources for your web application.  Such
  a class loader must follow the requirements of the Servlet
  Specification, and load classes from the following locations:</p>
  <ul>
  <li>From the <code>/WEB-INF/classes</code> directory inside your
      web application.</li>
  <li>From JAR files in the <code>/WEB-INF/lib</code> directory
      inside your web application.</li>
  <li>From resources made available by Catalina to all web
      applications globally.</li>
  </ul>

  <p>A Loader element MAY be nested inside a <a href="context.html">Context</a>
  component.  If it is not included, a default Loader configuration will be
  created automatically, which is sufficient for most requirements.</p>

  <p>For a more in-depth description of the class loader hierarchy
  that is implemented by Catalina, see <a href="../class-loader-howto.html">the ClassLoader HowTo</a>.</p>

    <p><em>The description below uses the variable name $CATALINA_BASE to refer the
    base directory against which most relative paths are resolved. If you have
    not configured Tomcat for multiple instances by setting a CATALINA_BASE
    directory, then $CATALINA_BASE will be set to the value of $CATALINA_HOME,
    the directory into which you have installed Tomcat.</em></p>

</div><h3 id="Attributes">Attributes</h3><div class="text">

  <div class="subsection"><h4 id="Common_Attributes">Common Attributes</h4><div class="text">

    <p>All implementations of <strong>Loader</strong>
    support the following attributes:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Common Attributes_className"><td><code class="attributeName">className</code></td><td>
        <p>Java class name of the implementation to use.  This class must
        implement the <code>org.apache.catalina.Loader</code> interface.
        If not specified, the standard value (defined below) will be used.</p>
      </td></tr><tr id="Attributes_Common Attributes_delegate"><td><code class="attributeName">delegate</code></td><td>
        <p>Set to <code>true</code> if you want the class loader to follow
        the standard Java2 delegation model, and attempt to load classes from
        parent class loaders <strong>before</strong> looking inside the web
        application.  Set to <code>false</code> (the default) to have the
        class loader look inside the web application first, before asking
        parent class loaders to find requested classes or resources.</p>
      </td></tr><tr id="Attributes_Common Attributes_reloadable"><td><code class="attributeName">reloadable</code></td><td>
        <p><strong>NOTE</strong> - This attribute has been deprecated in Tomcat
        9 and will be removed in Tomcat 10. Use the equivalent attribute on the
        surrounding <a href="context.html">Context</a> component.</p>

        <p>Set to <code>true</code> if you want Catalina to monitor classes in
        <code>/WEB-INF/classes/</code> and <code>/WEB-INF/lib</code> for
        changes, and automatically reload the web application if a change
        is detected.  This feature is very useful during application
        development, but it requires significant runtime overhead and is
        not recommended for use on deployed production applications.  You
        can use the <a href="../manager-howto.html">Manager</a> web
        application, however, to trigger reloads of deployed applications
        on demand.</p>

        <p><strong>NOTE</strong> - The value for this property will be
        inherited from the <code>reloadable</code> attribute you set on
        the surrounding <a href="context.html">Context</a> component,
        and any value you explicitly set here will be replaced.</p>
      </td></tr></table>

  </div></div>


  <div class="subsection"><h4 id="Standard_Implementation">Standard Implementation</h4><div class="text">

    <p>The standard implementation of <strong>Loader</strong> is
    <strong>org.apache.catalina.loader.WebappLoader</strong>.
    It supports the following additional attributes (in addition to the
    common attributes listed above):</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Standard Implementation_loaderClass"><td><code class="attributeName">loaderClass</code></td><td>
        <p>Java class name of the <code>java.lang.ClassLoader</code>
        implementation class to use. Custom implementations must extend
        <code>org.apache.catalina.loader.WebappClassLoaderBase</code>.
        </p>

        <p>If not specified, the default value is
        <code>org.apache.catalina.loader.ParallelWebappClassLoader</code>. The
        default <strong>loaderClass</strong> is parallel capable, which
        means that multiple threads may load difference classes in parallel.
        A non-parallel capable <strong>loaderClass</strong> is available and can
        be used by specifying
        <code>org.apache.catalina.loader.WebappClassLoader</code>.</p>
      </td></tr></table>

  </div></div>

</div><h3 id="Nested_Components">Nested Components</h3><div class="text">

  <p>No components may be nested inside a <strong>Loader</strong> element.</p>

</div><h3 id="Special_Features">Special Features</h3><div class="text">

  <div class="subsection"><h4 id="Logging">Logging</h4><div class="text">

    <p>A loader is associated with the log category based on its classname.</p>

  </div></div>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>