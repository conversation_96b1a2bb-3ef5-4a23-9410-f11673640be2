{"java.configuration.updateBuildConfiguration": "automatic", "bitoAI.codeCompletion.enableAutoCompletion": false, "java.compile.nullAnalysis.mode": "automatic", "java.debug.settings.enableRunDebugCodeLens": true, "java.debug.settings.hotCodeReplace": "auto", "java.debug.settings.onBuildFailureProceed": true, "java.test.config": {"vmArgs": ["-Duntill.home=${workspaceFolder}/test-config"]}, "files.exclude": {"**/build-gradle": true, "**/node_modules": true, "**/.gradle": true}, "java.project.sourcePaths": ["src-backend/main/java", "src-backend/test/java"], "java.project.outputPath": "build-gradle/classes", "java.project.referencedLibraries": ["build-gradle/libs/*.jar"]}