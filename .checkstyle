<?xml version="1.0" encoding="UTF-8"?>

<fileset-config file-format-version="1.2.0" simple-config="true" sync-formatter="false">
  <local-check-config name="checkstyle" location="checkstyle.xml" type="project" description="">
    <additional-data name="protect-config-file" value="false"/>
  </local-check-config>
  <fileset name="all" enabled="true" check-config-name="checkstyle" local="true">
    <file-match-pattern match-pattern="." include-pattern="true"/>
  </fileset>
  <filter name="FilesFromPackage" enabled="true">
    <filter-data value="src/main/resources"/>
    <filter-data value="src/test/resources"/>
  </filter>
</fileset-config>
