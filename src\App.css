* {
  font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif
}

html,
body {
  width: 100%;
  height: 100%;
}

#root {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

#top {
  background-color: #464646;
  font-size: 1.5rem;
  height: 5rem;
  position: relative;
  -webkit-box-shadow: 0px 2px 15px 0px rgba(50, 50, 50, 0.81);
  -moz-box-shadow: 0px 2px 15px 0px rgba(50, 50, 50, 0.81);
  box-shadow: 0px 2px 15px 0px rgba(50, 50, 50, 0.81);

  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

#logo {
  color: #fff;
  line-height: 5rem;
  padding-left: 7rem;
  background-image: url(logo-simple.png);
  background-position: 1.3em center;
  background-repeat: no-repeat;
  background-size: 3.5rem;

  width: 240px;
}

#lang {
  /*  width: 5rem; */
  padding: 1rem;
}

#lang img {
  width: 3rem;
  height: 3rem;
  border: 0.2rem solid #e0f358;
  border-radius: 3rem;
  cursor: pointer;
}

#tabs {
  height: 5rem;
  vertical-align: bottom;
  flex: 1;
}

#work {
  display: flex;
  flex-direction: row;

  flex: 1;

  width: 100%;
}

#sidebar {
  display: flex;
  flex-direction: column;

  height: 100%;
  width: 15rem;

  background-color: #323232;
}

#toolbar {
  text-align: center;
  color: #fff;

  display: flex;
  flex-direction: row;
  justify-content: space-between;

  padding: 5px 15px;
}

#form {
  font-size: 1rem;

  flex: 1;

  height: calc(100vh - 5rem);
  overflow: auto;
}

.empty-list {
  display: flex;
  flex: 1;

  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.empty-list .ant-empty-description {
  font-size: 1.3rem;
  color: #989898;
}

#period {
  height: auto;
  position: relative;
  padding: 0px 0px 10px;
  background-color: #464646;
  margin: 0px 1em;
}

#filters {}

#new_connection {
  padding: 20px 5%;
}

.tab {
  height: 100%;
  float: left;
  position: relative;
  line-height: 5rem;
  color: #fff;
  box-sizing: border-box;
  cursor: pointer;
}

.tabTitle {
  padding: 0 2rem;
}

#activeTab {
  color: #e0f358;
  border-bottom: 0.3em solid #e0f358;
  cursor: default;
  background-color: #323232;
}

.tabLegend {
  position: relative;
  top: -1em;
  font-size: 0.5em;
  line-height: 0.5em;
  text-align: center;
  width: 100%;
}

.tabClose {
  position: absolute;
  top: 0.1rem;
  right: 0.1rem;
  line-height: 1rem;
  cursor: pointer;
}

.error {
  font-size: 1em;
  color: #990000;
  padding: 1em 0;
}

.expandIcon {
  vertical-align: middle
}

pre {
  font-family: 'Courier New', Courier, monospace
}

.row:nth-child(odd) {
  background-color: #f7f9eb;
}

.row:hover {
  background-color: #f7f9cf;
}

.row .left {
  height: 3rem;
  font-size: 0.8rem;
  float: left;
  width: 12rem;
  text-align: right;
  padding-right: 2rem;
  vertical-align: middle;
}

.row .left .date {
  margin-top: 0.3rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.row .right {
  height: 3rem;
  float: right;
  width: 10rem;
  text-align: right;
  line-height: 3rem;
  padding-right: 1rem;
}

.row .right .hht {
  line-height: 1rem;
  font-size: .8rem;
  margin-top: .6rem;
}

.row .right .hhtpc {
  line-height: 1rem;
  font-size: .8rem;
}

.row .rightmost {
  height: 3rem;
  float: right;
  width: 2rem;
  text-align: right;
  line-height: 3rem;
  font-size: 2em;
  padding-right: 1rem;
}

.row .center {
  height: 3rem;
  line-height: 3rem;
  overflow: hidden;
}

.pkind,
.pkindActive {
  float: left;
  width: 50%;
  text-align: center;
  font-size: 1.3rem;
  line-height: 2em;
  height: 2em;
  box-sizing: border-box;
  background-color: #323232;
}

.pkind {
  color: #fff;
  cursor: pointer;
  border-bottom: 1px solid #999;
}

.periodKinds {
  height: 2em;
  font-size: 1.3rem;
}

.pkindActive {
  color: #e0f358;
  border-bottom: 0.3em solid #e0f358;
}

.periodDay {
  text-align: center;
  font-size: 1.3rem;
  line-height: 4em;
  height: 4em;
  color: #fff;
}

.unval {
  color: #aaa;
}

.valid {
  color: #00cc00;
}

.invalid {
  color: #cc0000;
}

.rangeLine {
  display: flex;
  flex-direction: row;

  align-items: center;
}

.rangeCenter {
  color: #ffffff;
  font-size: 1.3em;
  line-height: 1.3em;
  text-align: center;
  width: 100%;
}

.inputTime input {
  color: #ffffff;
  font-size: 1.3em;
}

.inputTime input::-webkit-clear-button {
  display: none;
}

.rangeLine {
  position: relative;
}

.rangeLeft {}

.rangeRight {}

.inp-label {
  color: #e0f358 !important;
}

.inp-label2 {
  color: #e0f358 !important;
  white-space: nowrap;
}

.input-line {
  border-bottom: 1px solid white;
  margin: 0 1em;
  height: 4.3rem;
}

.input-line2 {
  margin: 1em 0 0 1em;
}

.inp-arrow {
  display: none !important;
}

.cancelIcon {
  cursor: pointer;
  color: white;
}

.toolbarIcon {
  cursor: pointer;
  color: white;
}

.inp-switch {
  color: #e0f358 !important;
}

.inp-switch-label {
  color: #ffffff !important;
  font-size: 1.3rem;
}

.inp-switch2 {
  color: #eee;
  background-color: #999999;
}

.timePicker {
  background-color: #464646;
  color: white;
  font-size: 1.3rem;
  width: 5rem;
  line-height: 2rem;
  outline: none;
  border: none;
  margin-left: 1rem;
}

.datePicker {
  background-color: #464646;
  color: white;
  font-size: 1.3rem;
  width: 8rem;
  line-height: 2rem;
  outline: none;
  border: none;
}

.dateTimePicker {
  background-color: #464646;
  color: white;
  font-size: 1.1rem;
  display: block;
  line-height: 2rem;
  outline: none;
  border: none;
  width: 100%;
  padding-left: 0.5rem;
  box-sizing: border-box;
}

.datePickerLabel {
  margin: 0.4rem 0 0 0.5rem;
  font-size: 1rem;
  color: #e0f358 !important;
}

.datePickerLabelError {
  font-size: 0.8rem;
  color: tomato;
  padding-left: 10px;
}

.react-datepicker-popper {
  width: 25rem;
}

ul {
  padding-inline-start: 0;
}

.table {
  min-width: 820px;
}

/* grid_table */

.grid_table {
  display: grid;
  column-gap: 2rem;
  row-gap: 1rem;

  width: 100%;
}

.grid_table.col_2 {
  grid-template-columns: auto auto;
}

.grid_table .label {
  font-weight: bold;
  width: 10rem;
}

.grid_table .value {
  overflow-y: auto;
}

/* flex_table */

.flex_table {
  display: flex;
  flex-direction: column;
}

.flex_table .row {
  display: flex;
  flex-direction: row;
}

.flex_table .row .cell {
    flex-wrap: nowrap;
    padding: 0.5rem
}

.flex_table .row .cell.header {
  font-weight: bold;
}

.flex_table .row .cell.label {
  width: 10rem;
  font-weight: bold;
}

.flex_table .row .cell.value {
  flex: 1;
  overflow-y: auto;
}