import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
    actionSwitchPeriodKind,
    actionIncDay,
    actionPeriodChangeValue,
    actionPeriodChangeRangeValue,
    actionDropTabRecords
} from '../actions/MainActions';
import { withStyles } from '@material-ui/core/styles';
import Icon from '@material-ui/core/Icon';
import IconButton from '@material-ui/core/IconButton';
import { rc } from '../lib/utils';
import DatePicker from 'react-datepicker';
import moment from 'moment';
import 'react-datepicker/dist/react-datepicker.css';
import 'moment/locale/fr';
import 'moment/locale/de';

const styles = (theme) => ({
    button: {
        margin: 'none',
        color: '#fff',
    },
    textField: {
        marginLeft: theme.spacing.unit * 2,
        marginRight: theme.spacing.unit,
        width: '4.7em',
    },
    textField2: {
        marginLeft: theme.spacing.unit,
        marginRight: 0,
        width: '4.7em',
    },
    textField3: {
        marginLeft: 0,
        marginRight: 0,
        color: '#ffffff',
        width: '8em',
    },
    input: {
        color: '#ffffff',
        fontSize: '1.3em',
    },
    fromDate: {
        color: '#ffffff',
        marginLeft: theme.spacing.unit,
        marginRight: theme.spacing.unit,
        marginTop: 2 * theme.spacing.unit,
        width: '14.5em',
    },
});


const DAY = 1000 * 3600 * 24;

class Period extends Component {

    constructor(props) {
        super(props);

        this.state = {
            till: false,
            from: false
        };
    }


    handlePeriodDateChanged(mm, kind) {
        const { range, sessionId } = this.props;
        let isErr = false;

        if (kind === 'till') {
            const d = range['from'];
            if (mm.isSameOrBefore(d)) {
                isErr = true;
            }
        } else {
            const d = range['till'];

            if (mm.isSameOrAfter(d)) {
                isErr = true;
            }
        }

        if (isErr) {
            this.setState({ [kind]: true });
            this.props.actionDropTabRecords(sessionId);
        } else {
            this.setState({ [kind]: false });
            this.props.actionPeriodChangeRangeValue(sessionId, kind, mm.toDate());
        }
    }

    incDay() {
        this.props.actionIncDay(this.props.sessionId, DAY);
    }

    decDay() {
        this.props.actionIncDay(this.props.sessionId, -DAY);
    }

    pad(number) {
        if (number < 10) {
            return `0${number}`;
        }
        return number;
    }

    getIsoDate(date) {
        return `${date.getFullYear()}-${this.pad(date.getMonth() + 1)}-${this.pad(date.getDate())}`;
    }

    getIsoDateTime(date) {
        return `${date.getFullYear()}-${this.pad(date.getMonth() + 1)}-${this.pad(date.getDate())}T${this.pad(date.getHours())}:${this.pad(date.getMinutes())}`;
    }

    // this.props.day.date
    renderDay() {
        const { classes } = this.props;

        return (
            <div>
                <div className="rangeLine">
                   
                    <div className="rangeLeft" style={{ top: '0.7rem' }}>
                        <IconButton className={classes.button} onClick={this.decDay.bind(this)}>
                            <Icon className="arrowIcon">arrow_back</Icon>
                        </IconButton>
                    </div>

                    <div className="rangeCenter" style={{ top: '1.1rem' }}>
                        <DatePicker
                            locale={this.props.locale}
                            selected={moment(this.props.day.date)}
                            className={'datePicker'}
                            onChange={(mm) => {
                                this.props.actionPeriodChangeValue(this.props.sessionId,
                                    'date', mm.toDate())
                            }}
                        />
                    </div>

                    <div className="rangeRight" style={{ top: '0.7rem' }}>
                        <IconButton className={classes.button} onClick={this.incDay.bind(this)}>
                            <Icon className="arrowIcon">arrow_forward</Icon>
                        </IconButton>
                    </div>
                </div>
                <div className="rangeLine">

                    <div className="rangeLeft" style={{ top: '5rem' }}>
                        <DatePicker
                            showTimeSelect
                            showTimeSelectOnly
                            timeCaption={rc(this.props.strings, 'time')}
                            locale={this.props.locale}
                            value={this.props.day.from}
                            timeIntervals={30}
                            className={'timePicker'}
                            timeFormat="HH:mm"
                            onChange={(mm) => { this.props.actionPeriodChangeValue(this.props.sessionId, 'from', mm.format("HH:mm")) }}
                        />
                    </div>

                    <div className="rangeCenter" style={{ top: '5.25rem' }}>
                        &mdash;
                    </div>
                    
                    <div className="rangeRight" style={{ top: '5rem' }}>
                        <DatePicker
                            showTimeSelect
                            showTimeSelectOnly
                            timeCaption={rc(this.props.strings, 'time')}
                            locale={this.props.locale}
                            value={this.props.day.till}
                            timeIntervals={30}
                            className={'timePicker'}
                            timeFormat="HH:mm"

                            onChange={(mm) => { this.props.actionPeriodChangeValue(this.props.sessionId, 'till', mm.format("HH:mm")) }}
                        />
                    </div>
                </div>
            </div>
        );
    }

    renderPeriodError() {
        return <span class="datePickerLabelError">{rc(this.props.strings, 'wrongDateSelected')}</span>;
    }

    renderPeriod() {
        return (
            <div>
                <div className="datePickerLabel">
                    {rc(this.props.strings, 'from')}
                    {this.state.from ? this.renderPeriodError() : null}
                </div>
                <DatePicker
                    locale={this.props.locale}
                    showTimeSelect
                    selected={moment(this.props.range.from)}
                    timeFormat="HH:mm"
                    className={'dateTimePicker'}
                    dateFormat="DD.MM.YYYY HH:mm"
                    timeCaption={rc(this.props.strings, 'time')}
                    onChange={(mm) => this.handlePeriodDateChanged(mm, 'from')}
                />
                <div className="datePickerLabel">
                    {rc(this.props.strings, 'till')}
                    {this.state.till ? this.renderPeriodError() : null}
                </div>
                <DatePicker
                    locale={this.props.locale}
                    showTimeSelect
                    selected={moment(this.props.range.till)}
                    timeFormat="HH:mm"
                    className={'dateTimePicker'}
                    dateFormat="DD.MM.YYYY HH:mm"
                    timeCaption={rc(this.props.strings, 'time')}
                    onChange={(mm) => this.handlePeriodDateChanged(mm, 'till')}
                />

            </div>
        );
    }

    render() {
        const period = this.props.kind === 'day'
            ? this.renderDay()
            : this.renderPeriod();
        return (
            <div>
                <div className="periodKinds">
                    <div
                        className={this.props.kind === 'day'
                            ? "pkindActive"
                            : "pkind"}
                        onClick={() => { this.props.actionSwitchPeriodKind(this.props.sessionId, 'day') }}
                    >
                        {rc(this.props.strings, 'day')}
                    </div>
                    <div
                        className={this.props.kind === 'range'
                            ? "pkindActive"
                            : "pkind"}
                        onClick={() => { this.props.actionSwitchPeriodKind(this.props.sessionId, 'range') }}
                    >
                        {rc(this.props.strings, 'range')}
                    </div>
                </div>
                {period}
            </div>
        );
    }
}

const mapStateToProps = (state) => {
    // console.log('Period mapStateToProps, state is ', state);
    const tab = state.tabs.items.find((tt) => tt.sessionId === state.tabs.active);
    const { kind, day, range } = tab.period;
    const { sessionId } = tab;
    return {
        kind,
        day,
        range,
        sessionId,
        strings: state.global.strings,
        locale: state.global.locale
    }
};

export default connect(mapStateToProps, {
    actionSwitchPeriodKind,
    actionIncDay,
    actionPeriodChangeValue,
    actionPeriodChangeRangeValue,
    actionDropTabRecords,
})(withStyles(styles)(Period));