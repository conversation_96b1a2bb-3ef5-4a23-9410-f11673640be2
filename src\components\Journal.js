import _ from 'lodash';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import { connect } from 'react-redux';
import CircularProgress from '@material-ui/core/CircularProgress';
import InfiniteScroll from 'react-infinite-scroller';
import { Empty } from 'antd';

import {
    doRequest,
    //actionLoadRecords,
    actionShowRecordDetails,
    actionCloseRecordDetails,
    //createRequestFromState
} from '../actions/MainActions';
import { rc, isValidated } from '../lib/utils';
import { printRecords } from '../utils/io';

import JournalDetails from './JournalDetails';

const styles = theme => ({
    container: {
        display: 'flex',
        flexWrap: 'wrap',
    },
    progress: {
        margin: theme.spacing.unit * 2,
    },
    root: {
        width: '100%',
        marginTop: theme.spacing.unit * 3,
    },
    table: {
        minWidth: 820,
    },
    tableWrapper: {
        overflowX: 'auto',
    },
});

class Journal extends Component {
    componentDidMount() {
        const { sessionId } = this.props;

        this.props.doRequest(sessionId, 0);
    }

    getFilter() {
        let from = null, till = null;

        console.log('PERIOD: ', this.props.period);

        if (this.props.period.kind === 'day') {
            from = new Date(this.props.period.day.date.getTime());
            from.setHours(this.props.period.day.from.hh, this.props.period.day.from.mm, 0, 0);

            till = new Date(this.props.period.day.date.getTime());
            till.setHours(this.props.period.day.till.hh, this.props.period.day.till.mm, 59, 999);
        }
        return {
            locale: 'en',
            search: null,
            kind: null,
            dateFrom: from,
            dateTo: till,
            pcName: null,
            user: null,
            billNumber: null,
            orderNumber: null,
            transactionNumber: null,
            hhtIp: null,
            exactRecordId: null,
        }
    }

    /*
    request(from) {
        const { host, port, locale, sessionId, period, validation, filters, recordsPerPage } = this.props;
        const request = createRequestFromState(
            { host, port, locale },
            { sessionId, period, validation, filters },
            from, recordsPerPage);
        this.props.actionLoadRecords(request);
    }
    */

    loadItems() {
        this.props.doRequest(this.props.sessionId, this.props.records.length);
        //this.request(this.props.records.length);
    }

    handleRowClick = (idx) => {
        this.props.actionShowRecordDetails(this.props.sessionId, idx);
    }

    handleCloseDetails = () => {
        this.props.actionCloseRecordDetails(this.props.sessionId);
    }

    printDetails(record) {
        printRecords([record], this.props.strings.entryKinds);
    }

    getItems() {
        const { records, sessionId } = this.props;

        if (!_.isArray(records) || records.length === 0) {
            return [];
        }

        return records.map((record, i) => {
            let valClass = 'unval';

            if (this.props.validation) {
                const valid = isValidated(sessionId, record.id);
                if (valid !== null) {
                    valClass = valid === true ? 'valid' : 'invalid';
                }
            }

            let pc;

            if (record.hhtAddr && record.hhtAddr.length > 0) {
                pc = (
                    <div className="right">
                        <div className="hht">{record.hhtAddr}</div>
                        <div className="hhtpc">({record.pc})</div>
                    </div>
                );
            } else {
                pc = (
                    <div className="right">{record.pc}</div>
                );
            }

            return (
                <div
                    key={i}
                    className="row"
                    onClick={this.handleRowClick.bind(this, i)}
                >
                    <div className="left">
                        <div className="date">
                            {new Date(record.date).toLocaleString()}
                        </div>
                        <div className="user">
                            {record.user}
                        </div>
                    </div>
                    <div className="rightmost">
                        <span id={`v${record.id}`} className={valClass}>&#8226;</span>
                    </div>
                    {pc}
                    <div className="center">
                        {record.header}
                    </div>
                </div>
            );
        });
    }

    renderNoData() {
        return (
            <div className="empty-list">
                <Empty description={rc(this.props.strings, 'noListItems')} />
            </div>
        );
    }

    render() {
        const { classes } = this.props;

        if (this.props.loading) {
            return (
                <CircularProgress className={classes.progress} />
            );
        }

        if ((this.props.loadingError || '').length > 0) {
            return (
                <div className="error">{this.props.loadingError}</div>
            );
        }

        const { hasMore } = this.props;

        const items = this.getItems();

        const ticket = (this.props.detailsRecord) > -1 ? this.props.records[this.props.detailsRecord].ticket : "";
        const changedValues = (this.props.detailsRecord) > -1 ? this.props.records[this.props.detailsRecord].changedValues : [];
        const fields = (this.props.detailsRecord) > -1 ? this.props.records[this.props.detailsRecord].fields : [];

        const detailsData = {
            strings: this.props.strings,
            ticket: ticket,
            changedValues: changedValues,
            fields: fields,
            hasTicket: ticket.length > 0,
            hasChanges: changedValues.length > 0,
            hasFields: Object.keys(fields).length > 0,
        }


        if (_.isArray(items) && items.length > 0) {
            return (
                <div id="journal_list">
                    <InfiniteScroll
                        pageStart={0}
                        loadMore={this.loadItems.bind(this)}
                        hasMore={hasMore}
                        loader={<CircularProgress className={classes.progress} key='loader' />}
                        useWindow={false}
                    >
                        <div className="records">
                            {_.isArray(items) && items.length > 0 ? items : this.renderNoData()}
                        </div>
                    </InfiniteScroll>

                    <JournalDetails
                        open={this.props.detailsOpen === true}
                        onClose={this.handleCloseDetails}
                        data={detailsData}
                    />
                </div>
            );
        }

        return this.renderNoData();
    }
}

const mapStateToProps = (state) => {
    const tab = state.tabs.items.find((tt) => tt.sessionId === state.tabs.active);
    return {
        host: state.global.host,
        port: state.global.port,
        sessionId: tab.sessionId,
        records: tab.data.records,
        loading: tab.data.loading,
        loadingError: tab.data.loadingError,
        hasMore: tab.data.hasMore,
        recordsPerPage: tab.data.recordsPerPage,
        validation: tab.validation,
        filters: tab.filters,
        page: tab.data.page,
        height: state.global.clientHeight,
        locale: state.global.locale,
        strings: state.global.strings,
        detailsOpen: tab.data.detailsOpen,
        detailsRecord: tab.data.detailsRecord,
        period: tab.period
    }
};

Journal.propTypes = {
    classes: PropTypes.object.isRequired,
};

export default connect(mapStateToProps, {
    doRequest,
    //actionLoadRecords,
    actionShowRecordDetails,
    actionCloseRecordDetails
})(withStyles(styles)(Journal));