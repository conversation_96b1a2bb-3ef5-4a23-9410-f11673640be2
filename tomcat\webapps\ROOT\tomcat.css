/*
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/
body {
    margin: 10px 20px;
    text-align: center;
    font-family: Arial, sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol {
    margin: 0 0 0.5em;
}

h1 {
    font-size: 18pt;
    margin: 0.5em 0 0;
}

h2 {
    font-size: 16pt;
}

h3 {
    font-size: 13pt;
}

h4 {
    font-size: 12pt;
}

h5 {
    font-size: 11pt;
}

p {
    font-size: 11pt
}

ul {
    margin: 0;
    padding: 0 0 0 0.25em;
    text-indent: 0;
    list-style: none;
}

li {
    margin: 0;
    padding: 0 0 0.25em;
    text-indent: 0;
    font-size: 80%;
}

pre {
    text-indent: 0.25em;
    width: 90%;
    font-size: 90%;
}

br.separator {
    margin: 0;
    padding: 0;
    clear: both;
}

a img {
    border: 0 none;
}

.container {
    padding: 10px;
    margin: 0 0 10px;
}

.col20 {
    float: left;
    width: 20%;
}

.col25 {
    float: left;
    width: 25%;
}

#wrapper {
    display: block;
    margin: 0 auto;
    text-align: left;
    min-width: 720px;
    max-width: 1000px;
}

.curved {
    border-radius: 10px;
}

#tomcat-logo {
    width: 150px;
    height: 106px;
}

#navigation {
    background: #eee url(bg-nav.png) repeat-x top left;
    margin: 0 0 10px;
    padding: 0;
}

#navigation span {
    float: left;
}

#navigation span a {
    display: block;
    padding: 10px;
    font-weight: bold;
    text-shadow: 1px 1px 1px #fff;
}

#navigation span a:link,
#navigation span a:visited,
#navigation span a:hover,
#navigation span a:active {
    color: #666;
    text-decoration: none;
}

#navigation span#nav-help {
    float: right;
    margin-right: 0;
}

#asf-box {
    height: 40px;
    background: #fff url(asf-logo-wide.svg) no-repeat top right;
}

#asf-box h1 {
    margin: 0;
    line-height: 1.5;
}

#upper {
    background: #fff url(bg-upper.png) repeat-x top left;
}

#congrats {
    text-align: center;
    padding: 10px;
    margin: 0 40px 20px;
    background-color: #9c9;
}

#congrats h2 {
    font-size: 14pt;
    padding: 0;
    margin: 0;
    color: #fff;
}

#notice {
    float: left;
    width: 560px;
    color: #696;
}

#notice a:link,
#notice a:visited,
#notice a:hover,
#notice a:active {
    color: #090;
    text-decoration: none;
}

#notice img,
#notice #tasks {
    float: left;
}

#tasks a:link,
#tasks a:visited,
#tasks a:hover,
#tasks a:active {
    text-decoration: underline;
}

#notice img {
    margin-right: 20px;
}

#actions {
    float: right;
    width: 140px;
}

#actions .button {
    display: block;
    padding: 0;
    height: 36px;
    background: url(bg-button.png) no-repeat top left;
}

#actions .button a {
    display: block;
    padding: 0;
}

#actions .button a:link,
#actions .button a:visited,
#actions .button a:hover,
#actions .button a:active {
    color: #696;
    text-decoration: none;
}

#actions .button a span {
    display: block;
    padding: 6px 10px;
    color: #666;
    text-shadow: 1px 1px 1px #fff;
    font-size: 10pt;
    font-weight: bold;
}

#middle {
    background: #eef url(bg-middle.png) repeat-x top left;
    margin: 20px 0;
    padding: 1px 10px;
}

#middle h3 {
    margin: 0 0 10px;
    color: #033;
}

#middle p {
    font-size: 10pt;
}

#middle a:link,
#middle a:visited,
#middle a:hover,
#middle a:active {
    color: #366;
    font-weight: bold;
}

#middle .col25 .container {
    padding: 0 0 1px;
}

#developers {
    float: left;
    width: 40%;
}

#security {
    float: right;
    width: 50%;
}

#lower {
    padding: 0;
}

#lower a:link,
#lower a:visited,
#lower a:hover,
#lower a:active {
    color: #600;
}

#lower strong a:link,
#lower strong a:visited,
#lower strong a:hover,
#lower strong a:active {
    color: #c00;
}

#lower h3 {
    color: #963;
    font-size: 14pt;
}

#lower h4 {
    font-size: 12pt;
}

#lower ul {
    padding: 0;
    margin: 0.5em 0;
}

#lower p,
#lower li {
    font-size: 9pt;
    color: #753;
    margin: 0 0 0.1em;
}

#lower li {
    padding: 3px 5px;
}

#lower li strong {
    color: #a53;
}

#lower li#list-announce {
    border: 1px solid #f90;
    background-color: #ffe8c8;
}

#lower p {
    font-size: 10.5pt;
}

#low-manage,
#low-docs,
#low-help {
    float: left;
    width: 32%;
}

#low-docs {
    margin: 0 0 0 2.2%;
}

#low-help {
    float: right;
}

#low-manage div,
#low-docs div,
#low-help div {
    min-height: 280px;
    border: 3px solid #ffdc75;
    background-color: #fff1c8;
    padding: 10px;
}

#footer {
    padding: 0;
    margin: 20px 0;
    color: #999;
    background-color: #eee;
}

#footer h4 {
    margin: 0 0 10px;
    font-size: 10pt;
}

#footer p {
    margin: 0 0 10px;
    font-size: 10pt;
}

#footer ul {
    margin: 6px 0 1px;
    padding: 0;
}

#footer li {
    margin: 0;
    font-size: 9pt;
}

#footer a:link,
#footer a:visited,
#footer a:hover,
#footer a:active {
    color: #666;
}

.copyright {
    font-size: 10pt;
    color: #666;
}