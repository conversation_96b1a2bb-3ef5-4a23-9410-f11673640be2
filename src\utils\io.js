import { saveAs } from 'file-saver';
import { rc } from './../lib/utils';
import { parse } from 'json2csv';

function getFieldDetails(record) {
    let out = "";
    if (record.fields) {
        for (const key of Object.keys(record.fields)) {
            out = `${out}${key}: ${record.fields[key]}\r\n`;
        }
    }
    return out;
}

function getChangedValuesDetails(record) {
    let out = "";
    if (record.changedValues) {
        for (const cv of record.changedValues) {
            out = `${out}${cv.name}: ${cv.oldValue} -> ${cv.newValue}\r\n`;
        }
    }
    return out;
}

function getTicketDetails(record) {
    if (record.ticket && record.ticket.trim() !== "") {
        return `${record.ticket.replace(/\n/g, "\r\n")}\r\n`;
    }
    return "";
}

function getTextDetails(record) {
    if (record.text && record.text.trim() !== "") {
        return `${record.text.replace("@", "\r\n")}\r\n`;
    }
    return "";
}

function getDetailsText(record) {
    return `${getFieldDetails(record)}${getChangedValuesDetails(record)}${getTicketDetails(record)}${getTextDetails(record)}`;
  }

  function recordsPrintable(records, entryKinds) {
    let text = "";
    for (const record of records) {

        const details = getDetailsText(record);
        let pre = "";
        if (details !== "") {
            const pre0 = document.createElement("pre");
            pre0.textContent = details.trim();
            pre = `<pre><code>${pre0.innerText}</code></pre>`;
        }

        text = `${text}
            <p>
                ${new Date(record.date).toLocaleString()}, ${record.user} (${record.pc}) ${entryKinds[record.kind]}: ${record.header} ${pre}
            </p><hr/>`;
    }
    return text;
}

export function printRecords(records, entryKinds) {
    const mywindow = window.open('', 'PRINT', '');  
    mywindow.document.write(`<html><head><title>${document.title}</title>`);
    mywindow.document.write('</head><body >');
    mywindow.document.write(recordsPrintable(records, entryKinds));
    mywindow.document.write('<script type="text/javascript">window.onload = function() { window.print(); window.close(); };</script>');
    mywindow.document.write('</body></html>');

    // necessary for IE >= 10
    mywindow.document.close(); 
    
    // necessary for IE >= 10*/  
    mywindow.focus(); 
//    mywindow.close();
//    mywindow.print();

    return true; 
}

function getPlainText(records, strings) {
    let text = "";
    for (const record of records) {

        let details = getDetailsText(record);
        if (details !== "") {
            details = `\r\n${details}`;
        }   
        text = `${text}${new Date(record.date).toLocaleString()}, ${record.user} (${record.pc}) ${strings.entryKinds[record.kind]}: "${record.header}" ${details}\r\n\r\n`;
    }

    return text;
}

function getCsv(records, strings) {

    const csvRecords = [];
    for (const record of records) {
        let details = record.header;
        const rDetails = getDetailsText(record);
        if (rDetails.length > 0) {
            details = `${details}\n${rDetails}`;
        }
        csvRecords.push({
            time: new Date(record.date).toLocaleString(),
            pc: record.pc,
            kind: strings.entryKinds[record.kind],
            user: record.user,
            details
        });
    }

    const fields = [
      {
        label: rc(strings, 'time'),
        value: 'time'
      },
      {
        label: rc(strings, 'pc'),
        value: 'pc'
      },
      {
        label: rc(strings, 'kind'),
        value: 'kind'
      },
      {
        label: rc(strings, 'user'),
        value: 'user'
      },
      {
        label: rc(strings, 'details'),
        value: 'details'
      },
    ];

    return parse(csvRecords, { fields });
}

export function downloadText(records, strings) {
    const blob = new Blob([getPlainText(records, strings)], { type: "text/plain;charset=utf-8" });
    saveAs(blob, "journal.txt");
}

export function downloadCsv(records, strings) {
    const csv = getCsv(records, strings);
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8" });
    saveAs(blob, "journal.csv");
}