<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;html>
  &lt;head>
    &lt;title>JSP 2.0 Expression Language - Basic Comparisons&lt;/title>
  &lt;/head>
  &lt;body>
    &lt;h1>JSP 2.0 Expression Language - Basic Comparisons&lt;/h1>
    &lt;hr>
    This example illustrates basic Expression Language comparisons.
    The following comparison operators are supported:
    &lt;ul>
      &lt;li>Less-than (&amp;lt; or lt)&lt;/li>
      &lt;li>Greater-than (&amp;gt; or gt)&lt;/li>
      &lt;li>Less-than-or-equal (&amp;lt;= or le)&lt;/li>
      &lt;li>Greater-than-or-equal (&amp;gt;= or ge)&lt;/li>
      &lt;li>Equal (== or eq)&lt;/li>
      &lt;li>Not Equal (!= or ne)&lt;/li>
    &lt;/ul>
    &lt;blockquote>
      &lt;u>&lt;b>Numeric&lt;/b>&lt;/u>
      &lt;code>
        &lt;table border="1">
          &lt;thead>
        &lt;td>&lt;b>EL Expression&lt;/b>&lt;/td>
        &lt;td>&lt;b>Result&lt;/b>&lt;/td>
      &lt;/thead>
      &lt;tr>
        &lt;td>\${1 &amp;lt; 2}&lt;/td>
        &lt;td>${1 &lt; 2}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${1 lt 2}&lt;/td>
        &lt;td>${1 lt 2}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${1 &amp;gt; (4/2)}&lt;/td>
        &lt;td>${1 > (4/2)}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${1 gt (4/2)}&lt;/td>
        &lt;td>${1 gt (4/2)}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${4.0 &amp;gt;= 3}&lt;/td>
        &lt;td>${4.0 >= 3}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${4.0 ge 3}&lt;/td>
        &lt;td>${4.0 ge 3}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${4 &amp;lt;= 3}&lt;/td>
        &lt;td>${4 &lt;= 3}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${4 le 3}&lt;/td>
        &lt;td>${4 le 3}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${100.0 == 100}&lt;/td>
        &lt;td>${100.0 == 100}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${100.0 eq 100}&lt;/td>
        &lt;td>${100.0 eq 100}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${(10*10) != 100}&lt;/td>
        &lt;td>${(10*10) != 100}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${(10*10) ne 100}&lt;/td>
        &lt;td>${(10*10) ne 100}&lt;/td>
      &lt;/tr>
    &lt;/table>
      &lt;/code>
      &lt;br>
      &lt;u>&lt;b>Alphabetic&lt;/b>&lt;/u>
      &lt;code>
        &lt;table border="1">
          &lt;thead>
            &lt;td>&lt;b>EL Expression&lt;/b>&lt;/td>
            &lt;td>&lt;b>Result&lt;/b>&lt;/td>
          &lt;/thead>
          &lt;tr>
            &lt;td>\${'a' &amp;lt; 'b'}&lt;/td>
            &lt;td>${'a' &lt; 'b'}&lt;/td>
          &lt;/tr>
          &lt;tr>
            &lt;td>\${'hip' &amp;gt; 'hit'}&lt;/td>
            &lt;td>${'hip' > 'hit'}&lt;/td>
          &lt;/tr>
          &lt;tr>
            &lt;td>\${'4' &amp;gt; 3}&lt;/td>
            &lt;td>${'4' > 3}&lt;/td>
          &lt;/tr>
        &lt;/table>
      &lt;/code>
    &lt;/blockquote>
  &lt;/body>
&lt;/html>
</pre></body></html>