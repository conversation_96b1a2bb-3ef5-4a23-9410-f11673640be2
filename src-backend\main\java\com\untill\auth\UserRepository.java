package com.untill.auth;

import com.untill.config.UntillConfigReader;

import java.io.File;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Repository for managing users from Firebird databases
 */
public class UserRepository {
    private static final Logger LOGGER = Logger.getLogger(UserRepository.class.getName());
    
    private static final String FIREBIRD_DRIVER = "org.firebirdsql.jdbc.FBDriver";
    private static final String DB_PATTERN = "*.fdb";
    private static final long REFRESH_INTERVAL_MINUTES = 5;
    
    private static final String USER_QUERY = 
        "SELECT NAME, JLOG_PASSWORD, IS_ACTIVE, JLOG_VIEW " +
        "FROM UNTILL_USERS " +
        "WHERE IS_ACTIVE = 1 AND JLOG_VIEW = 1";
    
    private final ConcurrentHashMap<String, User> users = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private volatile boolean initialized = false;
    
    private static UserRepository instance;
    
    private UserRepository() {
        try {
            Class.forName(FIREBIRD_DRIVER);
            LOGGER.info("Firebird JDBC driver loaded successfully");
        } catch (ClassNotFoundException e) {
            LOGGER.log(Level.SEVERE, "Failed to load Firebird JDBC driver", e);
        }
        
        // Start periodic refresh
        scheduler.scheduleAtFixedRate(this::refreshUsers, 0, REFRESH_INTERVAL_MINUTES, TimeUnit.MINUTES);
    }
    
    public static synchronized UserRepository getInstance() {
        if (instance == null) {
            instance = new UserRepository();
        }
        return instance;
    }
    
    /**
     * Authenticate user with username and password
     */
    public boolean authenticate(String username, String password) {
        if (username == null || password == null) {
            return false;
        }
        
        User user = users.get(username.toUpperCase());
        if (user == null) {
            LOGGER.warning("User not found: " + username);
            return false;
        }
        
        if (!user.isValidForJlog()) {
            LOGGER.warning("User not valid for JLOG access: " + username);
            return false;
        }
        
        boolean authenticated = password.equals(user.getPassword());
        if (authenticated) {
            LOGGER.info("User authenticated successfully: " + username);
        } else {
            LOGGER.warning("Authentication failed for user: " + username);
        }
        
        return authenticated;
    }
    
    /**
     * Check if repository is initialized
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * Get number of loaded users
     */
    public int getUserCount() {
        return users.size();
    }
    
    /**
     * Refresh users from all Firebird databases
     */
    private void refreshUsers() {
        try {
            LOGGER.info("Starting user refresh...");
            
            String untillHome = UntillConfigReader.getUntillHome();
            if (untillHome == null || untillHome.trim().isEmpty()) {
                LOGGER.warning("untill.home property not set, cannot refresh users");
                return;
            }
            
            File dbDir = new File(untillHome, "DB");
            if (!dbDir.exists() || !dbDir.isDirectory()) {
                LOGGER.warning("DB directory not found: " + dbDir.getAbsolutePath());
                return;
            }
            
            File[] fdbFiles = dbDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".fdb"));
            if (fdbFiles == null || fdbFiles.length == 0) {
                LOGGER.warning("No .fdb files found in: " + dbDir.getAbsolutePath());
                return;
            }
            
            ConcurrentHashMap<String, User> newUsers = new ConcurrentHashMap<>();
            
            for (File fdbFile : fdbFiles) {
                try {
                    loadUsersFromDatabase(fdbFile, newUsers);
                } catch (Exception e) {
                    LOGGER.log(Level.WARNING, "Failed to load users from database: " + fdbFile.getName(), e);
                }
            }
            
            users.clear();
            users.putAll(newUsers);
            initialized = true;
            
            LOGGER.info("User refresh completed. Loaded " + users.size() + " users from " + fdbFiles.length + " databases");
            
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error during user refresh", e);
        }
    }
    
    /**
     * Load users from a specific Firebird database
     */
    private void loadUsersFromDatabase(File fdbFile, ConcurrentHashMap<String, User> userMap) throws SQLException {
        String jdbcUrl = "jdbc:firebirdsql:embedded:" + fdbFile.getAbsolutePath() + "?encoding=UTF8";
        
        LOGGER.fine("Connecting to database: " + fdbFile.getName());
        
        try (Connection conn = DriverManager.getConnection(jdbcUrl, "SYSDBA", "masterkey");
             PreparedStatement stmt = conn.prepareStatement(USER_QUERY);
             ResultSet rs = stmt.executeQuery()) {
            
            int userCount = 0;
            while (rs.next()) {
                String name = rs.getString("NAME");
                String password = rs.getString("JLOG_PASSWORD");
                boolean isActive = rs.getInt("IS_ACTIVE") == 1;
                boolean hasJlogView = rs.getInt("JLOG_VIEW") == 1;
                
                if (name != null && password != null && isActive && hasJlogView) {
                    User user = new User(name, password, isActive, hasJlogView);
                    userMap.put(name.toUpperCase(), user);
                    userCount++;
                    LOGGER.fine("Loaded user: " + name);
                }
            }
            
            LOGGER.info("Loaded " + userCount + " users from database: " + fdbFile.getName());
            
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "SQL error loading users from " + fdbFile.getName() + ": " + e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * Shutdown the repository
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
