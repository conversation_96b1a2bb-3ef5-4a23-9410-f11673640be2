import {
    ACTION_NEWCONN_CHANGE_PROP, ACTION_NEWCONN_START_CONNECTING, ACTION_NEWCONN_CONNECT_ERROR,
    ACTION_NEWCONN_CONNECT_SUCCESS, ACTION_DATABASE_OPENED, ACTION_DATABASE_JOURNAL_LOADING_SUCCESS,
    ACTION_DATABASE_JOURNAL_LOADING_ERROR, ACTION_GLOBAL_WINDOW_RESIZE, ACTION_DATABASE_JOURNAL_OPEN_DETAILS,
    ACTION_DATABASE_JOURNAL_CLOSE_DETAILS, ACTION_PERIOD_SWITCH_KIND,
    ACTION_PERIOD_INC_DAY,
    ACTION_PERIOD_CHANGE_DAY_VALUE,
    ACTION_PERIOD_CHANGE_RANGE_VALUE,
    ACTION_DATABASE_JOURNAL_LOADING_STARTED,
    ACTION_GLOBAL_SET_STRINGS,
    ACTION_FITLERS_CHANGE_VALUE,
    ACTION_FITLERS_CHANGE_VALIDATION,
    ACTION_DATABASE_CLOSE,
    ACTION_DATABASE_JOURNAL_DROP_RECORS
} from "./Types";

import {
    isValidated,
    validated,
} from '../lib/utils'

import { createAuthHeader } from './AuthActions';

// Helper function to build proper URL
function buildApiUrl(host, port) {
    return `${host}:${port}/jv2/UBL/jsapi`;
}

// Helper function to get auth credentials from state
function getAuthCredentials(getState) {
    const state = getState();
    const auth = state.auth;
    if (auth && auth.isAuthenticated && auth.username && auth.password) {
        return { username: auth.username, password: auth.password };
    }
    return null;
}

function getResponse(json) {

    // console.log('getResponse(json): ', json);

    if (json.jsonrpc !== '2.0') {
        throw new Error('Unexpected response syntax');
    }

    if (json.error) {
        throw new Error(json.error.message || 'Unknown service error');
    }

    if (!json.result) {
        throw new Error('Unexpected server response');
    }

    return json.result;

}

async function request(url, func, args, authCredentials = null) {
    const reqBody = JSON.stringify({
        jsonrpc: '2.0',
        method: func,
        params: args,
        id: 1
    });

    // console.log('fetching: ', url, reqBody);

    const headers = {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        'Accept-Charset': 'utf-8',
        Connection: 'close'
    };

    // Add authentication header if credentials provided
    if (authCredentials) {
        headers.Authorization = createAuthHeader(authCredentials.username, authCredentials.password);
    }

    const response = await fetch(url, {
        method: 'POST',
        cache: 'no-cache',
        headers,
        body: reqBody
    });

    if (response.status === 401) {
        throw new Error('Authentication failed');
    }

    const json = await response.json();

    return getResponse(json);
}

/* eslint max-params: "off" */
async function asyncConnect(dispatch, host, port, locale, cookieLocale, authCredentials) {
    try {
        const newLocale = locale || cookieLocale || 'en';
        const init = await request(buildApiUrl(host, port), 'jlogc_getContext2', [newLocale], authCredentials);
        dispatch({
            type: ACTION_GLOBAL_SET_STRINGS,
            payload: {
                strings: init,
                locale: newLocale
            }
        });

        const response = await request(buildApiUrl(host, port), 'getDatabases', [], authCredentials);
        dispatch({
            type: ACTION_NEWCONN_CONNECT_SUCCESS,
            payload: {
                items: response,
                host,
                port
            }
        });

    } catch (error) {
        dispatch({ type: ACTION_NEWCONN_CONNECT_ERROR, payload: error.message || 'Error' });
    }
}

async function asyncOpenDb(dispatch, { host, port, dbname, validation }, authCredentials) {
    try {
        const sessionId = await request(buildApiUrl(host, port), 'openSession', [dbname], authCredentials);
        const init = await request(buildApiUrl(host, port), 'jlog_jlogInit', [sessionId], authCredentials);

        dispatch({
            type: ACTION_DATABASE_OPENED,
            payload: {
                sessionId,
                init,
                dbname,
                validation
            }
        });

    } catch (error) {
        dispatch({ type: ACTION_NEWCONN_CONNECT_ERROR, payload: error.message || 'Error' });
    }
}

/* 
    Returns object { host, port, sessionId, filter, startFrom, count }
*/
const DAY = 1000 * 3600 * 24;
export function createRequestFromState({ host, port, locale }, { sessionId, period, validation, filters }, startFrom, count) {
    let { from, till } = period.range;

    if (period.kind === 'day') {
        from = new Date(period.day.date.getTime());

        const [
            hhFrom,
            mmFrom
        ] = period.day.from.split(":");
        from.setHours(hhFrom, mmFrom, 0, 0);

        till = new Date(period.day.date.getTime());
        const [
            hhTill,
            mmTill
        ] = period.day.till.split(":");

        till.setHours(hhTill, mmTill, 0, 0);
        till = new Date(till.getTime() - 1);

        const hmFrom = parseInt((hhFrom * 60) + mmFrom, 10);
        const hmTill = parseInt((hhTill * 60) + mmTill, 10);

        if (hmFrom >= hmTill) {
            till = new Date(till.getTime() + DAY);
        }
    } else {
        till.setSeconds(0, 0);
        till = new Date(till.getTime() - 1);
    }

    const filter = {
        'locale': locale || 'en',
        search: null,
        kind: filters.kind,
        dateFrom: from,
        dateTo: till,
        pcName: filters.pcName,
        user: filters.user,
        billNumber: filters.billNr,
        orderNumber: filters.orderNr,
        transactionNumber: filters.transactionNr,
        hhtIp: filters.hhtIp,
        exactRecordId: null,
    }

    return {
        host,
        port,
        sessionId,
        filter,
        startFrom,
        count,
        validation
    }
}

async function validate({ host, port, sessionId }, ids, authCredentials) {
    // console.log("validate: ", ids);
    const result = await request(buildApiUrl(host, port), 'jlog_validateJLogEntries',
        [
            sessionId,
            ids
        ], authCredentials);
    for (const key of Object.keys(result)) {
        validated(sessionId, key, result[key]);
    }
}

/* 
    request : object { host, port, sessionId, filter, startFrom, count }
*/
/* eslint max-statements: "off" */
async function asyncLoadJournal(dispatch, req, authCredentials) {
    try {
        const result = await request(buildApiUrl(req.host, req.port), 'jlog_getJLogEntriesUnvalidated',
            [
                req.sessionId,
                req.filter,
                req.startFrom,
                req.count
            ], authCredentials);

        const unvalidated = [];
        for (const rec of result) {
            const isval = isValidated(req.sessionId, rec.id);
            // console.log('isval ', rec.id, isval);
            if (isval === null) {
                unvalidated.push(rec.id);
            }
        }
        if (unvalidated.length > 0 && req.validation) {
            validate(req, unvalidated, authCredentials);
        }

        dispatch({
            type: ACTION_DATABASE_JOURNAL_LOADING_SUCCESS,
            payload: {
                sessionId: req.sessionId,
                append: req.startFrom > 0,
                result
            }
        });

    } catch (error) {
        dispatch({
            type: ACTION_DATABASE_JOURNAL_LOADING_ERROR,
            payload: {
                sessionId: req.sessionId,
                error: error.message || 'Error'
            }
        });
    }
}

export const actionNewConnectionChangeProp = (key, value) => ({
    type: ACTION_NEWCONN_CHANGE_PROP,
    payload: { key, value }
});

export const actionConnectToServer = (host, port, locale, cookieLocale) => (dispatch, getState) => {
    dispatch({ type: ACTION_NEWCONN_START_CONNECTING });
    const authCredentials = getAuthCredentials(getState);
    asyncConnect(dispatch, host, port, locale, cookieLocale, authCredentials);
};

export const actionOpenDatabase = ({ host, port, dbname, validation }) => (dispatch, getState) => {
    dispatch({ type: ACTION_NEWCONN_START_CONNECTING });
    const authCredentials = getAuthCredentials(getState);
    asyncOpenDb(dispatch, { host, port, dbname, validation }, authCredentials);
};

export const actionWindowResize = (headerHeight) => ({
    type: ACTION_GLOBAL_WINDOW_RESIZE,
    payload: { headerHeight }
});

async function reloadLang(host, port, newLocale, dispatch, authCredentials) {
    try {
        const init = await request(buildApiUrl(host, port), 'jlogc_getContext2', [newLocale], authCredentials);
        dispatch({
            type: ACTION_GLOBAL_SET_STRINGS,
            payload: {
                strings: init,
                locale: newLocale
            }
        });
    } catch (error) {
        console.error(error);
    }
}

export const actionSetLang = (host, port, lang) => (dispatch, getState) => {
    const authCredentials = getAuthCredentials(getState);
    reloadLang(host, port, lang, dispatch, authCredentials);
};


export const actionShowRecordDetails = (sessionId, index) => ({
    type: ACTION_DATABASE_JOURNAL_OPEN_DETAILS,
    payload: { sessionId, index }
});

export const actionCloseDatabase = (sessionId) => ({
    type: ACTION_DATABASE_CLOSE,
    payload: { sessionId }
});


export const actionCloseRecordDetails = (sessionId) => ({
    type: ACTION_DATABASE_JOURNAL_CLOSE_DETAILS,
    payload: { sessionId }
});

export const actionSwitchPeriodKind = (sessionId, kind) => (dispatch, getState) => {
    dispatch({
        type: ACTION_PERIOD_SWITCH_KIND,
        payload: { sessionId, kind }
    });
    reload(dispatch, getState);
};

export const doRequest = (sessionId, from = 0) => (dispatch, getState) => {
    const state = getState();
    const tab = state.tabs.items.find((tt) => tt.sessionId === sessionId);

    if (tab) {
        let host = state.global.host;
        let port = state.global.port;
        let recordsPerPage = tab.data.recordsPerPage;
        let validation = tab.validation;
        let filters = tab.filters;
        let locale = state.global.locale;
        let period = tab.period;

        const request = createRequestFromState(
                { host, port, locale },
                { sessionId, period, validation, filters },
                from, recordsPerPage);

        const authCredentials = getAuthCredentials(getState);
        actionLoadRecords(dispatch, request, authCredentials);
    }
    
}

export const actionLoadRecords = (dispatch, { host, port, sessionId, filter, startFrom, count, validation }, authCredentials) => {
    if (startFrom === 0) {
        dispatch({
            type: ACTION_DATABASE_JOURNAL_LOADING_STARTED,
            payload: { sessionId }
        });
    }
    asyncLoadJournal(dispatch, { host, port, sessionId, filter, startFrom, count, validation }, authCredentials);
};

function reload(dispatch, getState) {
    const state = getState();
    const tab = state.tabs.items.find((tt) => tt.sessionId === state.tabs.active);

    dispatch({
        type: ACTION_DATABASE_JOURNAL_LOADING_STARTED,
        payload: { sessionId: tab.sessionId }
    });

    const req = createRequestFromState(state.global, tab, 0, tab.data.recordsPerPage);
    const authCredentials = getAuthCredentials(getState);
    asyncLoadJournal(dispatch, req, authCredentials);
}

export const actionIncDay = (sessionId, ms) => (dispatch, getState) => {
    dispatch({
        type: ACTION_PERIOD_INC_DAY,
        payload: { sessionId, ms }
    });
    reload(dispatch, getState);
};

export const actionPeriodChangeValue = (sessionId, key, value) => (dispatch, getState) => {
    dispatch({
        type: ACTION_PERIOD_CHANGE_DAY_VALUE,
        payload: { sessionId, key, value }
    });
    reload(dispatch, getState);
};

export const actionPeriodChangeRangeValue = (sessionId, key, value) => (dispatch, getState) => {
    dispatch({
        type: ACTION_PERIOD_CHANGE_RANGE_VALUE,
        payload: { sessionId, key, value }
    });
    reload(dispatch, getState);
};

export const actionPeriodChangeFiltersValue = (sessionId, key, value) => (dispatch, getState) => {
    dispatch({
        type: ACTION_FITLERS_CHANGE_VALUE,
        payload: { sessionId, key, value }
    });
    reload(dispatch, getState);
};

export const actionPeriodChangeFiltersValidation = (sessionId, value) => (dispatch, getState) => {
    dispatch({
        type: ACTION_FITLERS_CHANGE_VALIDATION,
        payload: { sessionId, value }
    });
    reload(dispatch, getState);
};

export const actionDropTabRecords = (sessionId) => ({
    type: ACTION_DATABASE_JOURNAL_DROP_RECORS,
    payload: { sessionId },
});
