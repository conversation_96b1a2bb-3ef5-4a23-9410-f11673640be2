-- <PERSON><PERSON> script to create test Firebird database with UN<PERSON>LL_USERS table
-- Run this with isql or Flame<PERSON><PERSON><PERSON> to create test database

-- Create database (run this command separately in isql):
-- CREATE DATABASE 'C:\PRODUCTS\JViewer2\test-config\DB\test.fdb' USER 'SYSDBA' PASSWORD 'masterkey';

-- Connect to database and create table:
-- CONNECT 'C:\PRODUCTS\JViewer2\test-config\DB\test.fdb' USER 'SYSDBA' PASSWORD 'masterkey';

CREATE TABLE UNTILL_USERS (
    ID INTEGER NOT NULL PRIMARY KEY,
    NAME VARCHAR(50) NOT NULL,
    JLOG_PASSWORD VARCHAR(100),
    IS_ACTIVE INTEGER DEFAULT 0,
    JLOG_VIEW INTEGER DEFAULT 0
);

-- Insert test users
INSERT INTO UNTILL_USERS (ID, NAME, JLOG_PASSWORD, IS_ACTIVE, JLOG_VIEW) VALUES (1, 'admin', 'admin123', 1, 1);
INSERT INTO UNTILL_USERS (ID, NAME, JLOG_PASSWORD, IS_ACTIVE, JLOG_VIEW) VALUES (2, 'user1', 'password1', 1, 1);
INSERT INTO UNTILL_USERS (ID, NAME, JLOG_PASSWORD, IS_ACTIVE, JLOG_VIEW) VALUES (3, 'user2', 'password2', 1, 0);
INSERT INTO UNTILL_USERS (ID, NAME, JLOG_PASSWORD, IS_ACTIVE, JLOG_VIEW) VALUES (4, 'disabled', 'password3', 0, 1);
INSERT INTO UNTILL_USERS (ID, NAME, JLOG_PASSWORD, IS_ACTIVE, JLOG_VIEW) VALUES (5, 'test', 'test123', 1, 1);

COMMIT;
