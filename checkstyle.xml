<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN" "https://checkstyle.org/dtds/configuration_1_3.dtd">

<!--
    This configuration file was written by the eclipse-cs plugin configuration editor
-->
<!--
    Checkstyle-Configuration: checkstyle
    Description: none
-->
<module name="Checker">
  <property name="severity" value="warning"/>
  <property name="charset" value="UTF-8"/>
  <property name="tabWidth" value="1"/>
  <module name="TreeWalker">
    <module name="ConstantName"/>
    <module name="LocalFinalVariableName"/>
    <module name="LocalVariableName"/>
    <module name="MemberName">
      <property name="applyToPrivate" value="false"/>
    </module>
    <module name="MethodName"/>
    <module name="PackageName"/>
    <module name="ParameterName"/>
    <module name="StaticVariableName"/>
    <module name="TypeName"/>
    <module name="IllegalImport"/>
    <module name="RedundantImport"/>
    <module name="UnusedImports"/>
    <module name="MethodLength">
      <property name="max" value="200"/>
    </module>
    <module name="ParameterNumber">
      <property name="max" value="12"/>
    </module>
    <module name="EmptyForIteratorPad"/>
    <module name="GenericWhitespace"/>
    <module name="MethodParamPad"/>
    <module name="NoWhitespaceAfter"/>
    <module name="NoWhitespaceBefore"/>
    <module name="OperatorWrap"/>
    <module name="ParenPad"/>
    <module name="TypecastParenPad"/>
    <module name="WhitespaceAfter"/>
    <module name="WhitespaceAround"/>
    <module name="ModifierOrder"/>
    <module name="RedundantModifier"/>
    <module name="AvoidNestedBlocks"/>
    <module name="EmptyBlock">
      <property name="tokens" value="LITERAL_WHILE,LITERAL_TRY,LITERAL_FINALLY,LITERAL_DO,LITERAL_IF,LITERAL_ELSE,LITERAL_FOR,INSTANCE_INIT,STATIC_INIT,LITERAL_SWITCH"/>
    </module>
    <module name="LeftCurly"/>
    <module name="RightCurly"/>
    <module name="AvoidInlineConditionals">
      <property name="severity" value="ignore"/>
      <metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
    </module>
    <module name="EmptyStatement"/>
    <module name="EqualsHashCode"/>
    <module name="HiddenField">
      <property name="ignoreConstructorParameter" value="true"/>
      <property name="ignoreSetter" value="true"/>
      <property name="setterCanReturnItsClass" value="true"/>
    </module>
    <module name="IllegalInstantiation"/>
    <module name="InnerAssignment"/>
    <module name="MagicNumber">
      <property name="ignoreNumbers" value="-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 100, 16, 32, 64, 128, 255, 256, 512, 1000, 1024, 2048, 4096, 8192, 16384, 32764, 65535, 65536"/>
      <property name="ignoreHashCodeMethod" value="true"/>
      <property name="constantWaiverParentToken" value="TYPECAST,METHOD_CALL,EXPR,ARRAY_INIT,UNARY_MINUS,UNARY_PLUS,ELIST,STAR,ASSIGN,PLUS,MINUS,DIV,LITERAL_NEW"/>
    </module>
    <module name="MissingSwitchDefault"/>
    <module name="SimplifyBooleanExpression"/>
    <module name="SimplifyBooleanReturn"/>
    <module name="FinalClass"/>
    <module name="HideUtilityClassConstructor"/>
    <module name="InterfaceIsType"/>
    <module name="VisibilityModifier"/>
    <module name="ArrayTypeStyle"/>
    <module name="UpperEll"/>
    <module name="Indentation">
      <property name="arrayInitIndent" value="1"/>
      <property name="basicOffset" value="1"/>
      <property name="caseIndent" value="0"/>
      <property name="lineWrappingIndentation" value="2"/>
      <property name="throwsIndent" value="2"/>
    </module>
    <module name="MemberName">
      <property name="applyToPublic" value="false"/>
      <property name="applyToProtected" value="false"/>
      <property name="applyToPackage" value="false"/>
      <property name="format" value="^(:?m_)?[a-z][a-zA-Z0-9]*$"/>
    </module>
    <module name="RegexpSinglelineJava">
      <metadata name="net.sf.eclipsecs.core.comment" value="Tabs not as indent"/>
      <property name="format" value="(?&lt;=[^\t])\t+"/>
      <property name="message" value="Line has tabs not as indent"/>
      <property name="ignoreComments" value="true"/>
    </module>
    <module name="SuppressionCommentFilter"/>
  </module>
  <module name="LineLength">
    <property name="max" value="130"/>
    <property name="tabWidth" value="4"/>
  </module>
  <module name="Translation"/>
  <module name="FileLength"/>
  <module name="RegexpSingleline">
    <metadata name="net.sf.eclipsecs.core.comment" value="Trailing spaces"/>
    <property name="format" value="(?&lt;!\*)\s+$|\*\s\s+$"/>
    <property name="message" value="Line has trailing spaces."/>
  </module>
  <module name="RegexpSingleline">
    <property name="severity" value="ignore"/>
    <property name="format" value="^\t* "/>
    <property name="message" value="Line has spacebar to indent."/>
    <metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
  </module>
  <module name="SuppressionFilter">
    <property name="file" value="${samedir}/suppressions.xml"/>
  </module>
  <module name="RegexpSingleline">
    <metadata name="net.sf.eclipsecs.core.comment" value="Space instead of tabs"/>
    <property name="format" value="^ {4,}"/>
    <property name="message" value="Spaces should never be used for indentation. Use tabs instead"/>
  </module>
  <module name="RegexpSingleline">
    <metadata name="net.sf.eclipsecs.core.comment" value="Tabs after spaces"/>
    <property name="format" value=" +\t+"/>
    <property name="message" value="Line has tabs after spaces"/>
  </module>
  <module name="RegexpSingleline">
    <metadata name="net.sf.eclipsecs.core.comment" value="Tabs not as indent"/>
    <property name="severity" value="ignore"/>
    <property name="format" value="(?&lt;=[^\t])\t+"/>
    <property name="message" value="Line has tabs not as indent"/>
    <metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
  </module>
</module>
