# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Do not edit this file directly.
# To edit translations see: https://tomcat.apache.org/getinvolved.html#Translations

cookies.cookies=Votre navigateur retourne les cookies suivant :
cookies.make-cookie=Cr\u00e9ation d'un cookie \u00e0 retourner \u00e0 votre navigateur
cookies.name=Nom :
cookies.no-cookies=Votre navigateur ne retourne aucun cookie
cookies.set=Vous venez d'envoyer le cookie suivant \u00e0 votre navigateur :
cookies.title=Exemple d'utilisation de Cookies
cookies.value=Valeur :

helloworld.title=Salut le Monde !

requestheader.title=Exemple d'information sur les ent\u00eates de requ\u00eate

requestinfo.label.method=M\u00e9thode :
requestinfo.label.pathinfo=Info de chemin :
requestinfo.label.protocol=Protocole :
requestinfo.label.remoteaddr=Adresse distante :
requestinfo.label.requesturi=URI de requ\u00eate :
requestinfo.title=Exemple d'information sur la requ\u00eate

requestparams.firstname=Pr\u00e9nom :
requestparams.lastname=Nom :
requestparams.no-params=Pas de param\u00eatre, merci d'en saisir quelques-uns
requestparams.params-in-req=Param\u00eatres dans la requ\u00eate :
requestparams.title=Exemple de Requ\u00eate avec Param\u00e8tres

sessions.adddata=Ajouter des donn\u00e9es \u00e0 votre session
sessions.created=Cr\u00e9e le :
sessions.data=Les donn\u00e9es existantes dans votre session :
sessions.dataname=Nom de l'Attribut de Session :
sessions.datavalue=Valeur de l'Attribut de Session :
sessions.id=ID de Session :
sessions.lastaccessed=Dernier acc\u00e8s :
sessions.title=Exemple de Sessions
