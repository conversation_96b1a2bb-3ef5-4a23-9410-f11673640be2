<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 (9.0.106) - Windows Service How-To</title><meta name="author" content="Mladen Turk"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9</h1><div class="versionInfo">
            Version 9.0.106,
            <time datetime="2025-06-05">Jun 5 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 4.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 2.3 Javadocs</a></li><li><a href="elapi/index.html">EL 3.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 1.1 Javadocs</a></li><li><a href="jaspicapi/index.html">JASPIC 1.1 Javadocs</a></li><li><a href="annotationapi/index.html">Common Annotations 1.3 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Windows Service How-To</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Tomcat_monitor_application">Tomcat monitor application</a><ol><li><a href="#Tomcat_monitor_application/Command_line_directives">Command line directives</a></li></ol></li><li><a href="#Tomcat_service_application">Tomcat service application</a><ol><li><a href="#Tomcat_service_application/Command_line_directives">Command line directives</a></li><li><a href="#Command_line_parameters">Command line parameters</a></li></ol></li><li><a href="#Installing_services">Installing services</a></li><li><a href="#Updating_services">Updating services</a></li><li><a href="#Removing_services">Removing services</a></li><li><a href="#Debugging_services">Debugging services</a></li><li><a href="#Multiple_Instances">Multiple Instances</a></li></ul>
</div><h3 id="Tomcat_monitor_application">Tomcat monitor application</h3><div class="text">

  <p><b>Tomcat9w</b> is a GUI application for monitoring and
  configuring Tomcat services.</p>

  <div class="subsection"><h4 id="Tomcat_monitor_application/Command_line_directives">Command line directives</h4><div class="text">

  <p>Each command line directive is in the form of
  <code>//XX[//ServiceName]</code></p>

  <p>If the <code>//ServiceName</code> component is omitted, then the service
  name is assumed to be the name of the file less the w suffix. So the default
  service name is <code>Tomcat9</code>.</p>

  <p>The available command line directives are:</p>

  <table class="defaultTable">
    <tr>
      <td><b>//ES</b></td>
      <td>Edit service configuration</td>
      <td>This is the default operation. It is called if the no option is
          provided. Starts the GUI application which allows the service
          configuration to be modified, started and stopped.</td>
    </tr>
    <tr>
      <td><b>//MS</b></td>
      <td>Monitor service</td>
      <td>Starts the GUI application and minimizes it to the system tray.</td>
    </tr>
    <tr>
      <td><b>//MR</b></td>
      <td>Monitor &amp; run service</td>
      <td>Starts the GUI application and minimizes it to the system tray. Start
          the service if it is not currently running.</td>
    </tr>
    <tr>
      <td><b>//MQ</b></td>
      <td>Monitor quit</td>
      <td>Stop any running monitor for the service.</td>
    </tr>
  </table>
</div></div>

</div><h3 id="Tomcat_service_application">Tomcat service application</h3><div class="text">

  <p><b>Tomcat9</b> is a service application for running Tomcat
  9 as a Windows service.</p>

  <div class="subsection"><h4 id="Tomcat_service_application/Command_line_directives">Command line directives</h4><div class="text">

    <p>Each command line directive is in the form of
    <code>//XX[//ServiceName]</code></p>

    <p>The available command line directives are:</p>

    <table class="defaultTable">
      <tr>
        <td><b>//TS</b></td>
        <td>Run the service as console application</td>
        <td>This is the default operation. It is called if the no option is
            provided. The ServiceName is the name of the executable without
            exe suffix, meaning Tomcat9</td>
      </tr>
      <tr>
        <td><b>//RS</b></td>
        <td>Run the service</td>
        <td>Called only from ServiceManager</td>
      </tr>
      <tr>
        <td><b>//ES</b></td>
        <td>Start (execute) the service</td>
        <td></td>
      </tr>
      <tr>
        <td><b>//SS</b></td>
        <td>Stop the service</td>
        <td></td>
      </tr>
      <tr>
        <td><b>//US</b></td>
        <td>Update service parameters</td>
        <td></td>
      </tr>
      <tr>
        <td><b>//IS</b></td>
        <td>Install service</td>
        <td></td>
      </tr>
      <tr>
        <td><b>//DS</b></td>
        <td>Delete service</td>
        <td>Stops the service if running</td>
      </tr>
      <tr>
        <td><b>//PS</b></td>
        <td>Print service</td>
        <td>Prints the command to (re-)create the current configuration</td>
      </tr>
      <tr>
        <td><b>//PP[//seconds]</b></td>
        <td>Pause service</td>
        <td>Default is 60 seconds</td>
      </tr>
      <tr>
        <td><b>//VS</b></td>
        <td>Version</td>
        <td>Print version and exit</td>
      </tr>
      <tr>
        <td><b>//?</b></td>
        <td>Help</td>
        <td>Print usage and exit</td>
      </tr>
    </table>
  </div></div>

  <div class="subsection"><h4 id="Command_line_parameters">Command line parameters</h4><div class="text">

    <p>Each command line parameter is prefixed with <code>--</code>. If the
    command line parameter is prefixed with <code>++</code>, and the parameter
    supports multiple values, then it's value will be appended to the existing
    option. In the table below, parameters that support multiple values are
    prefixed with <code>++</code>.</p>

    <p>If the environment variable with the same name as command line parameter
    but prefixed with <code>PR_</code> exists it will take precedence. For
    example:</p>

    <div class="codeBox"><pre><code>set PR_CLASSPATH=xx.jar</code></pre></div>

    <p>is equivalent to providing</p>

    <div class="codeBox"><pre><code>--Classpath=xx.jar</code></pre></div>

    <p> as command line parameter.</p>

    <table class="defaultTable">
      <tr>
        <th>ParameterName</th>
        <th>Default</th>
        <th>Description</th>
      </tr>
      <tr>
        <td>--Description</td>
        <td></td>
        <td>Service name description (maximum 1024 characters)</td>
      </tr>
      <tr>
        <td>--DisplayName</td>
        <td>ServiceName</td>
        <td>Service display name</td>
      </tr>
      <tr>
        <td>--Install</td>
        <td>procrun.exe //RS//ServiceName</td>
        <td>Install image</td>
      </tr>
      <tr>
        <td>--Startup</td>
        <td>manual</td>
        <td>Service startup mode can be either <b>auto</b> or <b>manual</b></td>
      </tr>
      <tr>
        <td>++DependsOn</td>
        <td></td>
        <td>List of services that this service depend on. Dependent services are
            separated using either <b>#</b> or <b>;</b> characters</td>
      </tr>
      <tr>
        <td>++Environment</td>
        <td></td>
        <td>List of environment variables that will be provided to the service
            in the form <b>key=value</b>. They are separated using either
            <b>#</b> or <b>;</b> characters. If you need to use either the
            <b>#</b> or <b>;</b> character within a value then the entire value
            must be enclosed inside single quotes.</td>
      </tr>
      <tr>
        <td>--User</td>
        <td></td>
        <td>User account used for running executable. It is used only for
            StartMode <b>java</b> or <b>exe</b> and enables running applications
            as service under account without LogonAsService privilege.</td>
      </tr>
      <tr>
        <td>--Password</td>
        <td></td>
        <td>Password for user account set by --User parameter</td>
      </tr>
      <tr>
        <td>--ServiceUser</td>
        <td></td>
        <td>Specifies the name of the account under which the service should
            run. Use an account name in the form
            <code>DomainName\UserName</code>. The service process will be logged
            on as this user. if the account belongs to the built-in domain, you
            can specify <code>.\UserName</code>. Note that the Service Control
            Manager does not accept localised forms of the standard names so to
            use them you need to specify <code>NT Authority\LocalService</code>,
            <code>NT Authority\NetworkService</code> or <code>LocalSystem</code>
            as appropriate.</td>
      </tr>
      <tr>
        <td>--ServicePassword</td>
        <td></td>
        <td>Password for user account set by --ServiceUser parameter</td>
      </tr>
      <tr>
        <td>--LibraryPath</td>
        <td></td>
        <td>Directory added to the search path used to locate the DLLs for the
            JVM. This directory is added both in front of the <code>PATH</code>
            environment variable and as a parameter to the
            <code>SetDLLDirectory</code> function.</td>
      </tr>
      <tr>
        <td>--JavaHome</td>
        <td>JAVA_HOME</td>
        <td>Set a different JAVA_HOME than defined by JAVA_HOME environment
            variable</td>
      </tr>
      <tr>
        <td>--Jvm</td>
        <td>auto</td>
        <td>Use either <b>auto</b> (i.e. find the JVM from the Windows registry)
            or specify the full path to the <b>jvm.dll</b>.
            You can use the environment variable expansion here.</td>
      </tr>
      <tr>
        <td>++JvmOptions</td>
        <td>-Xrs</td>
        <td>List of options in the form of <b>-D</b> or <b>-X</b> that will be
            passed to the JVM. The options are separated using either
            <b>#</b> or <b>;</b> characters. If you need to embed either <b>#</b> or
            <b>;</b> characters, put them inside single quotes. (Not used in
            <b>exe</b> mode.)</td>
      </tr>
      <tr>
        <td>++JvmOptions9</td>
        <td></td>
        <td>List of options in the form of <b>-D</b> or <b>-X</b> that will be
            passed to the JVM when running on Java 9 or later. The options are
            separated using either <b>#</b> or <b>;</b> characters. If you need
            to embed either <b>#</b> or <b>;</b> characters, put them inside
            single quotes. (Not used in <b>exe</b> mode.)</td>
      </tr>
      <tr>
        <td>--Classpath</td>
        <td></td>
        <td>Set the Java classpath. (Not used in <b>exe</b> mode.)</td>
      </tr>
      <tr>
        <td>--JvmMs</td>
        <td></td>
        <td>Initial memory pool size in MiB. (Not used in <b>exe</b> mode.)</td>
      </tr>
      <tr>
        <td>--JvmMx</td>
        <td></td>
        <td>Maximum memory pool size in MiB. (Not used in <b>exe</b> mode.)</td>
      </tr>
      <tr>
        <td>--JvmSs</td>
        <td></td>
        <td>Thread stack size in KiB. (Not used in <b>exe</b> mode.)</td>
      </tr>
      <tr>
        <td>--StartMode</td>
        <td></td>
        <td>One of <b>jvm</b>, <b>Java</b> or <b>exe</b>. The modes are:
          <ul>
            <li>jvm - start Java in-process. Depends on jvm.dll, see
                <b>--Jvm</b>.</li>
            <li>Java - same as exe, but automatically uses the default Java
                executable, i.e. %JAVA_HOME%\bin\java.exe. Make sure JAVA_HOME
                is set correctly, or use --JavaHome to provide the correct
                location. If neither is set, procrun will try to find the
                default JDK (not JRE) from the Windows registry.</li>
            <li>exe - run the image as a separate process</li>
          </ul>
        </td>
      </tr>
      <tr>
        <td>--StartImage</td>
        <td></td>
        <td>Executable that will be run. Only applies to <b>exe</b> mode.</td>
      </tr>
      <tr>
        <td>--StartPath</td>
        <td></td>
        <td>Working path for the start image executable.</td>
      </tr>
      <tr>
        <td>--StartClass</td>
        <td>Main</td>
        <td>Class that contains the startup method. Applies to the <b>jvm</b> and
            <b>Java</b> modes. (Not used in <b>exe</b> mode.) </td>
      </tr>
      <tr>
        <td>--StartMethod</td>
        <td>main</td>
        <td>Method name if differs then main</td>
      </tr>
      <tr>
        <td>++StartParams</td>
        <td></td>
        <td>List of parameters that will be passed to either StartImage or
            StartClass. Parameters are separated using either <b>#</b> or
            <b>;</b> character.</td>
      </tr>
      <tr>
        <td>--StopMode</td>
        <td></td>
        <td>One of <b>jvm</b>, <b>Java</b> or <b>exe</b>. See <b>--StartMode</b>
            for further details. </td>
      </tr>
      <tr>
      <td>--StopImage</td>
        <td></td>
        <td>Executable that will be run on Stop service signal. Only applies to
            <b>exe</b> mode.</td>
      </tr>
      <tr>
        <td>--StopPath</td>
        <td></td>
        <td>Working path for the stop image executable. Does not apply to
            <b>jvm</b> mode.</td>
      </tr>
      <tr>
        <td>--StopClass</td>
        <td>Main</td>
        <td>Class that will be used on Stop service signal. Applies to the
            <b>jvm</b> and <b>Java</b> modes. </td>
      </tr>
      <tr>
        <td>--StopMethod</td>
        <td>main</td>
        <td>Method name if differs then main</td>
      </tr>
      <tr>
        <td>--StopParams</td>
        <td></td>
        <td>List of parameters that will be passed to either StopImage or
            StopClass. Parameters are separated using either <b>#</b> or
            <b>;</b> character.</td>
      </tr>
      <tr>
        <td>++StopTimeout</td>
        <td>No Timeout</td>
        <td>Defines the timeout in seconds that procrun waits for service to
            exit gracefully.</td>
      </tr>
      <tr>
        <td>--LogPath</td>
        <td>%SystemRoot%\System32\LogFiles\Apache</td>
        <td>Defines the path for logging. Creates the directory if
            necessary.</td>
      </tr>
      <tr>
        <td>--LogPrefix</td>
        <td>commons-daemon</td>
        <td>Defines the service log filename prefix. The log file is created in
            the LogPath directory with <code>.YEAR-MONTH-DAY.log</code>
            suffix</td>
      </tr>
      <tr>
        <td>--LogLevel</td>
        <td>Info</td>
        <td>Defines the logging level and can be either <b>Error</b>,
            <b>Info</b>, <b>Warn</b> or <b>Debug</b>. (Case insensitive).</td>
      </tr>
      <tr>
        <td>--LogJniMessages</td>
        <td>0</td>
        <td>Set this non-zero (e.g. 1) to capture JVM jni debug messages in the
            procrun log file. Is not needed if stdout/stderr redirection is
            being used. Only applies to jvm mode.</td>
      </tr>
      <tr>
        <td>--StdOutput</td>
        <td></td>
        <td>Redirected stdout filename.
            If named <b>auto</b> then file is created inside <b>LogPath</b> with
            the name <b>service-stdout.YEAR-MONTH-DAY.log</b>.</td>
      </tr>
      <tr>
        <td>--StdError</td>
        <td></td>
        <td>Redirected stderr filename.
            If named <b>auto</b> then file is created inside <b>LogPath</b> with
            the name <b>service-stderr.YEAR-MONTH-DAY.log</b>.</td>
      </tr>
      <tr>
        <td>--PidFile</td>
        <td></td>
        <td>Defines the file name for storing the running process id. Actual
        file is created in the <b>LogPath</b> directory</td>
      </tr>
    </table>
  </div></div>
</div><h3 id="Installing_services">Installing services</h3><div class="text">
<p>
The safest way to manually install the service is to use the provided
<b>service.bat</b> script. Administrator privileges are required to run this
script. If necessary, you can use the <code>/user</code> switch to specify
a user to use for the installation of the service.
</p>
<p>
<strong>NOTE:</strong> If User Account Control (UAC) is enabled you will be
asked for additional privileges when 'Tomcat9.exe' is launched by
the script.<br>
If you want to pass additional options to service installer as
<code>PR_*</code> environment variables, you have to either configure them
globally in OS, or launch the program that sets them with elevated privileges
(e.g. right-click on cmd.exe and select "Run as administrator"; on Windows 8
(or later) or Windows Server 2012 (or later), you can open an elevated command
prompt for the current directory from the Explorer
by clicking on the "File" menu bar). See issue <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=56143">56143</a> for details.
</p>

<div class="codeBox"><pre><code>Install the service named 'Tomcat9'
C:\&gt; service.bat install</code></pre></div>

<p>There is a 2nd optional parameter that lets you specify the name of the
service, as displayed in Windows services.</p>

<div class="codeBox"><pre><code>Install the service named 'MyService'
C:\&gt; service.bat install MyService</code></pre></div>

<p>When installing the service with a non-default name,
tomcat9.exe and tomcat9w.exe may be renamed to
match the chosen service name. To do this, use the <code>--rename</code>
option.</p>

<div class="codeBox"><pre><code>Install the service named 'MyService' with renaming
C:\&gt; service.bat install MyService --rename</code></pre></div>

<p>
If using tomcat9.exe, you need to use the <b>//IS</b> parameter.</p>

<div class="codeBox"><pre><code>Install the service named 'Tomcat9'
C:\&gt; tomcat9 //IS//Tomcat9 --DisplayName="Apache Tomcat 9" ^
     --Install="C:\Program Files\Tomcat\bin\tomcat9.exe" --Jvm=auto ^
     --StartMode=jvm --StopMode=jvm ^
     --StartClass=org.apache.catalina.startup.Bootstrap --StartParams=start ^
     --StopClass=org.apache.catalina.startup.Bootstrap --StopParams=stop</code></pre></div>

</div><h3 id="Updating_services">Updating services</h3><div class="text">
<p>
To update the service parameters, you need to use the <b>//US</b> parameter.
</p>

<div class="codeBox"><pre><code>Update the service named 'Tomcat9'
C:\&gt; tomcat9 //US//Tomcat9 --Description="Apache Tomcat Server - https://tomcat.apache.org/ " ^
     --Startup=auto --Classpath=%JAVA_HOME%\lib\tools.jar;%CATALINA_HOME%\bin\bootstrap.jar</code></pre></div>

<p>If you gave the service an optional name, you need to specify it like this:
</p>

<div class="codeBox"><pre><code>Update the service named 'MyService'
C:\&gt; tomcat9 //US//MyService --Description="Apache Tomcat Server - https://tomcat.apache.org/ " ^
     --Startup=auto --Classpath=%JAVA_HOME%\lib\tools.jar;%CATALINA_HOME%\bin\bootstrap.jar</code></pre></div>

</div><h3 id="Removing_services">Removing services</h3><div class="text">
<p>
To remove the service, you need to use the <b>//DS</b> parameter.<br>
If the service is running it will be stopped and then deleted.</p>

<div class="codeBox"><pre><code>Remove the service named 'Tomcat9'
C:\&gt; tomcat9 //DS//Tomcat9</code></pre></div>

<p>If you gave the service an optional name, you need to specify it like this:
</p>

<div class="codeBox"><pre><code>Remove the service named 'MyService'
C:\&gt; tomcat9 //DS//MyService</code></pre></div>

</div><h3 id="Debugging_services">Debugging services</h3><div class="text">
<p>
To run the service in console mode, you need to use the <b>//TS</b> parameter.
The service shutdown can be initiated by pressing <b>CTRL+C</b> or
<b>CTRL+BREAK</b>.
If you rename the tomcat9.exe to testservice.exe then you can just execute the
testservice.exe and this command mode will be executed by default.</p>

<div class="codeBox"><pre><code>Run the service named 'Tomcat9' in console mode
C:\&gt; tomcat9 //TS//Tomcat9 [additional arguments]
Or simply execute:
C:\&gt; tomcat9</code></pre></div>

</div><h3 id="Multiple_Instances">Multiple Instances</h3><div class="text">
<p>
Tomcat supports installation of multiple instances. You can have a single
installation of Tomcat with multiple instances running on different IP/port
combinations, or multiple Tomcat versions, each running one or more instances on
different IP/ports.</p>
<p>
Each instance folder will need the following structure:
</p>
<ul>
<li>conf</li>
<li>logs</li>
<li>temp</li>
<li>webapps</li>
<li>work</li>
</ul>
<p>
At a minimum, conf should contain a copy of the following files from
CATALINA_HOME\conf\. Any files not copied and edited, will be picked up by
default from CATALINA_HOME\conf, i.e. CATALINA_BASE\conf files override defaults
from CATALINA_HOME\conf.</p>
<ul>
<li>server.xml</li>
<li>web.xml</li>
</ul>
<p>
You must edit CATALINA_BASE\conf\server.xml to specify a unique IP/port for the
instance to listen on. Find the line that contains
<code>&lt;Connector port="8080" ...</code> and add an address attribute and/or
update the port number so as to specify a unique IP/port combination.</p>
<p>
To install an instance, first set the CATALINA_HOME environment variable to the
name of the Tomcat installation directory. Then create a second environment
variable CATALINA_BASE and point this to the instance folder. Then run
"service.bat install" command specifying a service name.</p>

<div class="codeBox"><pre><code>set CATALINA_HOME=c:\tomcat_9
set CATALINA_BASE=c:\tomcat_9\instances\instance1
service.bat install instance1</code></pre></div>

<p>
To modify the service settings, you can run <b>tomcat9w //ES//instance1</b>.
</p>
<p>
For additional instances, create additional instance folder, update the
CATALINA_BASE environment variable, and run the "service.bat install" again.</p>

<div class="codeBox"><pre><code>set CATALINA_BASE=c:\tomcat_9\instances\instance2
service.bat install instance2</code></pre></div>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>