<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee" version="3.0">
    
    <!-- Basic Proxy Servlet (without authentication) -->
    <servlet>
        <servlet-name>ProxyServlet</servlet-name>
        <servlet-class>com.untill.ProxyServlet</servlet-class>
        <init-param>
            <param-name>targetHost</param-name>
            <param-value>http://*************</param-value>
        </init-param>
        <init-param>
            <param-name>targetPort</param-name>
            <param-value>6116</param-value>
        </init-param>
    </servlet>
    
    <!-- Authenticated Proxy Servlet (with password protection) -->
    <servlet>
        <servlet-name>AuthenticatedProxyServlet</servlet-name>
        <servlet-class>com.untill.AuthenticatedProxyServlet</servlet-class>
        <init-param>
            <param-name>targetHost</param-name>
            <param-value>http://*************</param-value>
        </init-param>
        <init-param>
            <param-name>targetPort</param-name>
            <param-value>6116</param-value>
        </init-param>
        <!-- Authentication parameters -->
        <init-param>
            <param-name>authEnabled</param-name>
            <param-value>true</param-value>
        </init-param>
        <init-param>
            <param-name>authUsername</param-name>
            <param-value>admin</param-value>
        </init-param>
        <init-param>
            <param-name>authPassword</param-name>
            <param-value>secure123</param-value>
        </init-param>
    </servlet>
    
    <!-- Servlet mappings -->
    <servlet-mapping>
        <servlet-name>ProxyServlet</servlet-name>
        <url-pattern>/proxy/*</url-pattern>
    </servlet-mapping>
    
    <servlet-mapping>
        <servlet-name>AuthenticatedProxyServlet</servlet-name>
        <url-pattern>/secure-proxy/*</url-pattern>
    </servlet-mapping>
    
</web-app>
