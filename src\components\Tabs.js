import React, { Component } from 'react'; 
import { connect } from 'react-redux';
import { actionSwitchTab, actionNewTab } from '../actions/TabsActions';
import { actionWindowResize, actionCloseDatabase } from '../actions/MainActions';
import Icon from '@material-ui/core/Icon';
import { rc } from '../lib/utils';

class Tabs extends Component {

    componentDidMount() {
        const hh = window.document.getElementById('tabs').offsetHeight;
        this.props.actionWindowResize(hh + 20);
    }

    renderActiveTab(title, sessionId) {
        let caption = title;
        if (sessionId === 'new') {
            caption = rc(this.props.strings, 'open');
        }
        return (
            <div className="tab" id="activeTab" key={`tab${sessionId}`}>
                <div className="tabTitle">
                    {caption}
                </div>
                {this.getCloseButton(sessionId)}
            </div>
        );
    }

    getCloseButton(sessionId) {
        if (sessionId === 'new') {
            return "";
        }
        return (
            <div className="tabClose">
                <Icon onClick={(event) => {
                    event.stopPropagation(); 
                    this.props.actionCloseDatabase(sessionId)
                }}>
                    close
                </Icon>
            </div>
        );
    }

    renderInactiveTab(title, range, sessionId) {
        let caption = title;
        if (sessionId === 'new') {
            caption = rc(this.props.strings, 'open');
        }
        return (
            <div className="tab" onClick={() => {this.props.actionSwitchTab(sessionId)}} key={`tab${sessionId}`}>
                <div className="tabTitle">
                    {caption}
                </div>
                <div className="tabLegend">
                    {range}
                </div>
                {this.getCloseButton(sessionId)}
            </div>
        );
    }

    pad(number) {
        if (number < 10) {
          return `0${number}`;
        }
        return number;
    }

    getTabLegend(tab) {
        if (tab.period && tab.period.kind === 'day') {
            return tab.period.day.date.toLocaleDateString();
        }
        if (tab.period) {
            const { from, till } =  tab.period.range;
            if (from && till) {
                const fromStr = `${from.getFullYear()}/${this.pad(from.getMonth() + 1)}/${this.pad(from.getDate())}`;
                const tillStr = `${till.getFullYear()}/${this.pad(till.getMonth() + 1)}/${this.pad(till.getDate())}`;
                return `${fromStr} - ${tillStr}`;
            }
        }
        return "";
    }

    renderTabs() {
        const items = [];
        for (const tab of this.props.tabs) {
            if (this.props.active === tab.sessionId) {
                items.push(this.renderActiveTab(tab.title, tab.sessionId));
            } else {
                items.push(this.renderInactiveTab(tab.title, this.getTabLegend(tab), tab.sessionId));
            }
        }
        return (
            <div id="tabs">
                {items}
            </div>
        );
    }
    
    render() {
        return this.renderTabs();
    }
}

const mapStateToProps = (state) => ({
    active: state.tabs.active,
    strings: state.global.strings,
    tabs: state.tabs.items
});

export default connect(mapStateToProps, 
    { 
        actionSwitchTab, actionNewTab, actionWindowResize, actionCloseDatabase
    })(Tabs);