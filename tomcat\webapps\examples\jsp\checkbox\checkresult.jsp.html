<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;html>
&lt;body bgcolor="white">
&lt;font size=5 color="red">
&lt;%! String[] fruits; %>
&lt;jsp:useBean id="foo" scope="page" class="checkbox.CheckTest" />

&lt;jsp:setProperty name="foo" property="fruit" param="fruit" />
&lt;hr>
The checked fruits (got using request) are: &lt;br>
&lt;%
    fruits = request.getParameterValues("fruit");
%>
&lt;ul>
&lt;%
    if (fruits != null) {
        for (String fruit : fruits) {
%>
&lt;li>
&lt;%
            out.println (util.HTMLFilter.filter(fruit));
        }
    } else out.println ("none selected");
%>
&lt;/ul>
&lt;br>
&lt;hr>

The checked fruits (got using beans) are &lt;br>

&lt;%
        fruits = foo.getFruit();
%>
&lt;ul>
&lt;%
    if (!fruits[0].equals("1")) {
        for (String fruit : fruits) {
%>
&lt;li>
&lt;%
            out.println (util.HTMLFilter.filter(fruit));
        }
    } else {
        out.println ("none selected");
    }
%>
&lt;/ul>
&lt;/font>
&lt;/body>
&lt;/html>
</pre></body></html>