<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 Configuration Reference (9.0.106) - The CredentialHandler Component</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9 Configuration Reference</h1><div class="versionInfo">
            Version 9.0.106,
            <time datetime="2025-06-05">Jun 5 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="runtime-attributes.html">Runtime attributes</a></li><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">JASPIC</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>The CredentialHandler Component</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Attributes">Attributes</a><ol><li><a href="#Common_Attributes">Common Attributes</a></li><li><a href="#MessageDigestCredentialHandler">MessageDigestCredentialHandler</a></li><li><a href="#NestedCredentialHandler">NestedCredentialHandler</a></li><li><a href="#SecretKeyCredentialHandler">SecretKeyCredentialHandler</a></li></ol></li><li><a href="#Nested_Components">Nested Components</a></li><li><a href="#Special_Features">Special Features</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

  <p>The <strong>CredentialHandler</strong> element represents the component
  used by a <a href="realm.html">Realm</a> to compare a provided credential such
  as a password with the version of the credential stored by the
  <a href="realm.html">Realm</a>. The <strong>CredentialHandler</strong> can
  also be used to generate a new stored version of a given credential that would
  be required, for example, when adding a new user to a
  <a href="realm.html">Realm</a> or when changing a user's password.</p>

  <p>A <strong>CredentialHandler</strong> element MUST be nested inside a
  <a href="realm.html">Realm</a> component.  If it is not included,
  a default <strong>CredentialHandler</strong> will be created using the
  <strong>MessageDigestCredentialHandler</strong>.</p>

</div><h3 id="Attributes">Attributes</h3><div class="text">

  <div class="subsection"><h4 id="Common_Attributes">Common Attributes</h4><div class="text">

    <p>All implementations of <strong>CredentialHandler</strong> support the
    following attributes:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Common Attributes_className"><td><strong><code class="attributeName">className</code></strong></td><td>
        <p>Java class name of the implementation to use. This class must
        implement the <code>org.apache.catalina.CredentialHandler</code>
        interface.</p>
      </td></tr></table>

    <p>Unlike most Catalina components, there are several standard
    <strong>CredentialHandler</strong> implementations available.  As a result,
    if a <strong>CredentialHandler</strong> element is present then the
    <code>className</code> attribute MUST be used to select the implementation
    you wish to use.</p>

  </div></div>


  <div class="subsection"><h4 id="MessageDigestCredentialHandler">MessageDigestCredentialHandler</h4><div class="text">

    <p>The <strong>MessageDigestCredentialHandler</strong> is used when stored
    passwords are protected by a message digest. This credential handler
    supports the following forms of stored passwords:</p>
    <ul>
      <li><strong>plainText</strong> - the plain text credentials if no
      algorithm is specified</li>
      <li><strong>encodedCredential</strong> - a hex encoded digest of the
      password digested using the configured digest</li>
      <li><strong>{MD5}encodedCredential</strong> - a Base64 encoded MD5
      digest of the password</li>
      <li><strong>{SHA}encodedCredential</strong> - a Base64 encoded SHA1 digest
      of the password</li>
      <li><strong>{SSHA}encodedCredential</strong> - 20 character salt followed
      by the salted SHA1 digest Base64 encoded</li>
      <li><strong>salt$iterationCount$encodedCredential</strong> - a hex encoded
      salt, iteration code and a hex encoded credential, each separated by
      $</li>
    </ul>

    <p>If the stored password form does not include an iteration count then an
    iteration count of 1 is used.</p>

    <p>If the stored password form does not include salt then no salt is
    used.</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_MessageDigestCredentialHandler_algorithm"><td><code class="attributeName">algorithm</code></td><td>
        <p>The name of the <code>java.security.MessageDigest</code> algorithm
        used to encode user passwords stored in the database.  If not specified,
        user passwords are assumed to be stored in clear-text.</p>
      </td></tr><tr id="Attributes_MessageDigestCredentialHandler_encoding"><td><code class="attributeName">encoding</code></td><td>
        <p>Digesting the password requires that it is converted to bytes. This
        attribute determines the character encoding to use for conversions
        between characters and bytes. If not specified, UTF-8 will be used.</p>
      </td></tr><tr id="Attributes_MessageDigestCredentialHandler_iterations"><td><code class="attributeName">iterations</code></td><td>
        <p>The number of iterations to use when creating a new stored credential
        from a clear text credential.</p>
      </td></tr><tr id="Attributes_MessageDigestCredentialHandler_saltLength"><td><code class="attributeName">saltLength</code></td><td>
        <p>The length of the randomly generated salt to use when creating a
        new stored credential from a clear text credential.</p>
      </td></tr></table>

  </div></div>

  <div class="subsection"><h4 id="NestedCredentialHandler">NestedCredentialHandler</h4><div class="text">

    <p>The <strong>NestedCredentialHandler</strong> is an implementation of
    <strong>CredentialHandler</strong> that delegates to one or more
    sub-CredentialHandlers.</p>

    <p>Using the <strong>NestedCredentialHandler</strong> gives the developer
    the ability to combine multiple <strong>CredentialHandler</strong>s of the
    same or different types.</p>

    <p>Sub-CredentialHandlers are defined by nesting CredentialHandler elements
    inside the <code>CredentialHandler</code> element that defines the
    NestedCredentialHandler. Credentials will be matched against each
    <code>CredentialHandler</code> in the order they are listed. A match against
    any CredentialHandler will be sufficient for the credentials to be
    considered matched.</p>

  </div></div>

  <div class="subsection"><h4 id="SecretKeyCredentialHandler">SecretKeyCredentialHandler</h4><div class="text">

    <p>The <strong>SecretKeyCredentialHandler</strong> is used when stored
    passwords are built using <code>javax.crypto.SecretKeyFactory</code>. This
    credential handler supports the following forms of stored passwords:</p>
    <ul>
      <li><strong>salt$iterationCount$encodedCredential</strong> - a hex encoded
      salt, iteration code and a hex encoded credential, each separated by
      $</li>
    </ul>

    <p>If the stored password form does not include an iteration count then an
    iteration count of 1 is used.</p>

    <p>If the stored password form does not include salt then no salt is
    used.</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_SecretKeyCredentialHandler_algorithm"><td><code class="attributeName">algorithm</code></td><td>
        <p>The name of the secret key algorithm used to encode user passwords
        stored in the database.  If not specified, a default of
        <code>PBKDF2WithHmacSHA1</code> is used.</p>
      </td></tr><tr id="Attributes_SecretKeyCredentialHandler_keyLength"><td><code class="attributeName">keyLength</code></td><td>
        <p>The length of key to generate for the stored credential. If not
        specified, a default of <code>160</code> is used.</p>
      </td></tr><tr id="Attributes_SecretKeyCredentialHandler_iterations"><td><code class="attributeName">iterations</code></td><td>
        <p>The number of iterations to use when creating a new stored credential
        from a clear text credential.</p>
      </td></tr><tr id="Attributes_SecretKeyCredentialHandler_saltLength"><td><code class="attributeName">saltLength</code></td><td>
        <p>The length of the randomly generated salt to use when creating a
        new stored credential from a clear text credential.</p>
      </td></tr></table>

  </div></div>

</div><h3 id="Nested_Components">Nested Components</h3><div class="text">

  <p>If you are using the <em>NestedCredentialHandler Implementation</em> or a
  CredentialHandler that extends the NestedCredentialHandler one or more
  <strong>&lt;CredentialHandler&gt;</strong> elements may be nested inside it.
  </p>

</div><h3 id="Special_Features">Special Features</h3><div class="text">

  <p>No special features are associated with a
  <strong>CredentialHandler</strong> element.</p>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>