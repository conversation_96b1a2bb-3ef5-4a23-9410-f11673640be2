package com.untill.auth;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.logging.Logger;

/**
 * Utility class for Basic Authentication
 */
public class BasicAuthUtils {
    private static final Logger LOGGER = Logger.getLogger(BasicAuthUtils.class.getName());
    
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BASIC_PREFIX = "Basic ";
    
    /**
     * Credentials extracted from Basic Auth header
     */
    public static class Credentials {
        private final String username;
        private final String password;
        
        public Credentials(String username, String password) {
            this.username = username;
            this.password = password;
        }
        
        public String getUsername() {
            return username;
        }
        
        public String getPassword() {
            return password;
        }
    }
    
    /**
     * Extract credentials from Basic Authentication header
     */
    public static Credentials extractCredentials(HttpServletRequest request) {
        String authHeader = request.getHeader(AUTHORIZATION_HEADER);
        
        if (authHeader == null || !authHeader.startsWith(BASIC_PREFIX)) {
            LOGGER.fine("No Basic Auth header found");
            return null;
        }
        
        try {
            String encodedCredentials = authHeader.substring(BASIC_PREFIX.length());
            String decodedCredentials = new String(Base64.getDecoder().decode(encodedCredentials), StandardCharsets.UTF_8);
            
            int colonIndex = decodedCredentials.indexOf(':');
            if (colonIndex == -1) {
                LOGGER.warning("Invalid Basic Auth format - no colon separator");
                return null;
            }
            
            String username = decodedCredentials.substring(0, colonIndex);
            String password = decodedCredentials.substring(colonIndex + 1);
            
            LOGGER.fine("Extracted credentials for user: " + username);
            return new Credentials(username, password);
            
        } catch (Exception e) {
            LOGGER.warning("Failed to decode Basic Auth header: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Create Basic Auth header value
     */
    public static String createAuthHeader(String username, String password) {
        String credentials = username + ":" + password;
        String encoded = Base64.getEncoder().encodeToString(credentials.getBytes(StandardCharsets.UTF_8));
        return BASIC_PREFIX + encoded;
    }
}
