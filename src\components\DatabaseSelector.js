import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withStyles } from '@material-ui/core/styles';
import {
    Paper,
    Typography,
    List,
    ListItem,
    ListItemText,
    CircularProgress,
    Button
} from '@material-ui/core';
import { actionSelectDatabase, actionLoadDatabases } from '../actions/AuthActions';

const styles = (theme) => ({
    container: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        padding: theme.spacing(2)
    },
    paper: {
        padding: theme.spacing(4),
        maxWidth: 500,
        width: '100%',
        textAlign: 'center'
    },
    title: {
        marginBottom: theme.spacing(3),
        color: theme.palette.primary.main
    },
    list: {
        marginTop: theme.spacing(2),
        marginBottom: theme.spacing(2)
    },
    listItem: {
        border: '1px solid #e0e0e0',
        borderRadius: theme.spacing(1),
        marginBottom: theme.spacing(1),
        cursor: 'pointer',
        '&:hover': {
            backgroundColor: '#f0f0f0'
        }
    },
    selectedItem: {
        backgroundColor: theme.palette.primary.light,
        color: 'white',
        '&:hover': {
            backgroundColor: theme.palette.primary.main
        }
    },
    loadingContainer: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: theme.spacing(4)
    },
    errorText: {
        color: theme.palette.error.main,
        marginBottom: theme.spacing(2)
    },
    retryButton: {
        marginTop: theme.spacing(2)
    }
});

class DatabaseSelector extends Component {
    componentDidMount() {
        // Load databases if not already loaded
        if (!this.props.databasesLoaded && !this.props.isLoggingIn) {
            this.props.actionLoadDatabases();
        }
    }

    handleDatabaseSelect = (database) => {
        this.props.actionSelectDatabase(database);
        // Call parent callback if provided
        if (this.props.onDatabaseSelected) {
            this.props.onDatabaseSelected(database);
        }
    };

    handleRetry = () => {
        this.props.actionLoadDatabases();
    };

    render() {
        const { 
            classes, 
            databases, 
            selectedDatabase, 
            isLoggingIn, 
            databasesLoaded, 
            error 
        } = this.props;

        return (
            <div className={classes.container}>
                <Paper className={classes.paper} elevation={3}>
                    <Typography variant="h4" className={classes.title}>
                        unTill JViewer
                    </Typography>
                    
                    {isLoggingIn && !databasesLoaded && (
                        <div className={classes.loadingContainer}>
                            <CircularProgress />
                            <Typography variant="body1" style={{ marginLeft: 16 }}>
                                Loading databases...
                            </Typography>
                        </div>
                    )}
                    
                    {error && (
                        <div>
                            <Typography variant="body1" className={classes.errorText}>
                                {error}
                            </Typography>
                            <Button 
                                variant="contained" 
                                color="primary" 
                                onClick={this.handleRetry}
                                className={classes.retryButton}
                            >
                                Retry
                            </Button>
                        </div>
                    )}
                    
                    {databasesLoaded && databases.length > 0 && (
                        <div>
                            <Typography variant="h6" gutterBottom>
                                Select Database
                            </Typography>
                            <List className={classes.list}>
                                {databases.map((database, index) => (
                                    <ListItem
                                        key={index}
                                        className={`${classes.listItem} ${
                                            selectedDatabase && selectedDatabase.name === database.name 
                                                ? classes.selectedItem 
                                                : ''
                                        }`}
                                        onClick={() => this.handleDatabaseSelect(database)}
                                    >
                                        <ListItemText
                                            primary={database.name}
                                            secondary={database.description}
                                        />
                                    </ListItem>
                                ))}
                            </List>
                        </div>
                    )}
                    
                    {databasesLoaded && databases.length === 0 && !error && (
                        <Typography variant="body1">
                            No databases available
                        </Typography>
                    )}
                </Paper>
            </div>
        );
    }
}

const mapStateToProps = (state) => ({
    databases: state.auth.databases,
    selectedDatabase: state.auth.selectedDatabase,
    isLoggingIn: state.auth.isLoggingIn,
    databasesLoaded: state.auth.databasesLoaded,
    error: state.auth.error
});

export default connect(mapStateToProps, { 
    actionSelectDatabase, 
    actionLoadDatabases 
})(withStyles(styles)(DatabaseSelector));
