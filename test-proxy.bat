@echo off
REM Test script for ProxyServlet
REM This script tests the ProxyServlet functionality

echo Testing ProxyServlet...
echo.

REM Set test configuration
set UNTILL_HOME=%~dp0test-config
echo Using Untill.ini from: %UNTILL_HOME%

REM Build the project
echo Building project...
call gradlew.bat build war
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    exit /b 1
)

echo.
echo Build successful!
echo WAR file created in build-gradle\libs\

REM Show configuration that will be used
echo.
echo Configuration that will be loaded:
type "%UNTILL_HOME%\Untill.ini"

echo.
echo To test the ProxyServlet:
echo 1. Deploy the WAR file to Tomcat
echo 2. Start Tomcat with: -Duntill.home=%UNTILL_HOME%
echo 3. Test with: curl http://localhost:8080/JViewer2-X.X.X-SNAPSHOT/proxy/test
echo.
echo For debugging in IDE, use the provided launch configurations.

pause
