# Отладка ProxyServlet в различных IDE

Этот документ описывает, как настроить отладку ProxyServlet в различных средах разработки.

## Конфигурация

ProxyServlet теперь читает настройки из файла `Untill.ini` через системную переменную `untill.home`:

- **Системная переменная**: `untill.home` - путь к каталогу с файлом `Untill.ini`
- **Файл конфигурации**: `${untill.home}/Untill.ini`
- **Секция**: `[common]`
- **Параметр**: `port` - базовый порт Untill
- **Целевой порт**: `port + 5`
- **Целевой хост**: `localhost`

### Пример Untill.ini

```ini
[common]
port=6111
debug=true

[database]
host=localhost
port=5432
```

Результат: ProxyServlet будет перенаправлять запросы на `http://localhost:6116`

## Отладка в VS Code

### Предварительная настройка

1. Установите расширение "Extension Pack for Java"
2. Убедитесь, что переменная `JAVA_HOME` указывает на JDK 8+

### Конфигурации запуска

В файле `.vscode/launch.json` настроены следующие конфигурации:

#### 1. Debug ProxyServlet with Tomcat
Запускает Tomcat с развернутым WAR файлом для полной отладки сервлета.

**Использование:**
1. Соберите проект: `./gradlew war`
2. Запустите конфигурацию "Debug ProxyServlet with Tomcat"
3. Откройте браузер: `http://localhost:8080/JViewer2-X.X.X-SNAPSHOT/proxy/test`

#### 2. Debug ProxyServlet Tests
Запускает юнит-тесты с возможностью отладки.

#### 3. Debug Gradle Build
Отладка процесса сборки Gradle.

### Переменные окружения

Все конфигурации автоматически устанавливают:
```
-Duntill.home=${workspaceFolder}/test-config
```

## Отладка в IntelliJ IDEA

### Предварительная настройка

1. Откройте проект как Gradle проект
2. Убедитесь, что Project SDK установлен на Java 8+

### Конфигурации запуска

В папке `.idea/runConfigurations/` созданы готовые конфигурации:

#### 1. Debug ProxyServlet with Tomcat
- **Тип**: Application
- **Main class**: `org.apache.catalina.startup.Bootstrap`
- **VM options**: включают `-Duntill.home=$PROJECT_DIR$/test-config`
- **Program arguments**: `start`

#### 2. Debug ProxyServlet Tests
- **Тип**: JUnit
- **Test class**: `com.untill.ProxyServletTest`
- **VM options**: включают `-Duntill.home=$PROJECT_DIR$/test-config`

### Использование

1. Соберите проект: `./gradlew war`
2. Выберите нужную конфигурацию в Run/Debug Configurations
3. Нажмите Debug (Shift+F9)

## Отладка в Eclipse

### Предварительная настройка

1. Импортируйте проект как Gradle проект
2. Убедитесь, что Java Build Path настроен правильно

### Конфигурации запуска

В папке `.metadata/.plugins/org.eclipse.debug.core/.launches/` создана конфигурация:

#### Debug ProxyServlet with Tomcat
- **Тип**: Java Application
- **Main class**: `org.apache.catalina.startup.Bootstrap`
- **VM arguments**: включают `-Duntill.home=${workspace_loc:JViewer2}/test-config`

### Использование

1. Соберите проект: `./gradlew war`
2. Откройте Run → Debug Configurations
3. Выберите "Debug ProxyServlet with Tomcat"
4. Нажмите Debug

## Тестовая конфигурация

В папке `test-config/` создан файл `Untill.ini` для тестирования:

```ini
[common]
port=6111
debug=true
log_level=INFO
```

Это означает, что ProxyServlet будет перенаправлять на `http://localhost:6116`.

## Отладка без IDE

### Удаленная отладка

Все конфигурации включают параметры для удаленной отладки:
```
-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005
```

Вы можете подключиться к порту 5005 из любой IDE для удаленной отладки.

### Командная строка

```bash
# Установить переменную окружения
export JAVA_OPTS="-Duntill.home=/path/to/config -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"

# Собрать проект
./gradlew war

# Запустить Tomcat с отладкой
catalina.sh jpda start
```

## Проверка конфигурации

### Тест чтения конфигурации

Запустите тест `UntillConfigReaderTest` для проверки корректности чтения конфигурации:

```bash
./gradlew test --tests com.untill.config.UntillConfigReaderTest
```

### Логи

При запуске ProxyServlet выводит в лог информацию о загруженной конфигурации:

```
INFO: Loaded configuration from Untill.ini: UntillConfig{host='localhost', port=6116, source='/path/to/Untill.ini'}
INFO: ProxyServlet initialized with target: localhost:6116
```

## Устранение проблем

### Проблема: Конфигурация не загружается

**Решение:**
1. Проверьте, что системная переменная `untill.home` установлена
2. Убедитесь, что файл `Untill.ini` существует в указанной папке
3. Проверьте права доступа к файлу

### Проблема: Неправильный порт

**Решение:**
1. Проверьте значение параметра `port` в секции `[common]`
2. Убедитесь, что значение является числом
3. Помните: целевой порт = базовый порт + 5

### Проблема: Отладчик не подключается

**Решение:**
1. Убедитесь, что порт 5005 свободен
2. Проверьте, что JVM запущена с параметрами отладки
3. Попробуйте изменить порт отладки в конфигурации
