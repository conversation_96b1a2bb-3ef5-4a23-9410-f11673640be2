<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee
                      http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
  version="4.0"
  metadata-complete="true">

    <description>
      Servlet and JSP Examples.
    </description>
    <display-name>Servlet and JSP Examples</display-name>

    <request-character-encoding>UTF-8</request-character-encoding>

    <!-- Define example filters -->
    <filter>
        <filter-name>Timing Filter</filter-name>
        <filter-class>filters.ExampleFilter</filter-class>
        <init-param>
            <param-name>attribute</param-name>
            <param-value>filters.ExampleFilter</param-value>
        </init-param>
    </filter>

    <filter>
        <filter-name>Request Dumper Filter</filter-name>
        <filter-class>org.apache.catalina.filters.RequestDumperFilter</filter-class>
    </filter>

    <filter>
        <filter-name>Compression Filter</filter-name>
        <filter-class>compressionFilters.CompressionFilter</filter-class>
        <init-param>
            <param-name>compressionThreshold</param-name>
            <param-value>128</param-value>
        </init-param>
        <init-param>
            <param-name>compressionBuffer</param-name>
            <param-value>8192</param-value>
        </init-param>
        <init-param>
            <param-name>compressionMimeTypes</param-name>
            <param-value>text/html,text/plain,text/xml</param-value>
        </init-param>
        <init-param>
          <param-name>debug</param-name>
          <param-value>0</param-value>
        </init-param>
    </filter>

    <!-- Configured to set X-FRAME-OPTIONS. Disable HSTS in case it          -->
    <!-- interferes with an existing setting. Keep X-Content-Type-Options    -->
    <!-- and X-XSS-Protection as they are page specific.                     -->
    <filter>
        <filter-name>HTTP header security filter</filter-name>
        <filter-class>org.apache.catalina.filters.HttpHeaderSecurityFilter</filter-class>
        <async-supported>true</async-supported>
        <init-param>
            <param-name>hstsEnabled</param-name>
            <param-value>false</param-value>
        </init-param>
    </filter>

    <!-- Define filter mappings for the timing filters -->
    <!--
    <filter-mapping>
        <filter-name>Timing Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    -->

<!--
    <filter-mapping>
      <filter-name>Compression Filter</filter-name>
      <url-pattern>/CompressionTest</url-pattern>
    </filter-mapping>
-->

<!--
    <filter-mapping>
        <filter-name>Request Dumper Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
-->

    <!-- Enable header security filter for all requests -->
    <filter-mapping>
        <filter-name>HTTP header security filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- Define example application events listeners -->
    <listener>
        <listener-class>listeners.ContextListener</listener-class>
    </listener>
    <listener>
        <listener-class>listeners.SessionListener</listener-class>
    </listener>

    <!-- Define listeners required by examples -->
    <listener>
        <listener-class>async.AsyncStockContextListener</listener-class>
    </listener>

    <!-- Define servlets that are included in the example application -->

    <servlet>
      <servlet-name>ServletToJsp</servlet-name>
      <servlet-class>ServletToJsp</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>CompressionFilterTestServlet</servlet-name>
        <servlet-class>compressionFilters.CompressionFilterTestServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>HelloWorldExample</servlet-name>
        <servlet-class>HelloWorldExample</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>RequestInfoExample</servlet-name>
        <servlet-class>RequestInfoExample</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>RequestHeaderExample</servlet-name>
        <servlet-class>RequestHeaderExample</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>RequestParamExample</servlet-name>
        <servlet-class>RequestParamExample</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>CookieExample</servlet-name>
        <servlet-class>CookieExample</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>SessionExample</servlet-name>
        <servlet-class>SessionExample</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>CompressionFilterTestServlet</servlet-name>
        <url-pattern>/CompressionTest</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>HelloWorldExample</servlet-name>
        <url-pattern>/servlets/servlet/HelloWorldExample</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>RequestInfoExample</servlet-name>
        <url-pattern>/servlets/servlet/RequestInfoExample/*</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>RequestHeaderExample</servlet-name>
        <url-pattern>/servlets/servlet/RequestHeaderExample</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>RequestParamExample</servlet-name>
        <url-pattern>/servlets/servlet/RequestParamExample</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>CookieExample</servlet-name>
        <url-pattern>/servlets/servlet/CookieExample</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>SessionExample</servlet-name>
        <url-pattern>/servlets/servlet/SessionExample</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ServletToJsp</servlet-name>
        <url-pattern>/servletToJsp</url-pattern>
    </servlet-mapping>

    <jsp-config>
        <taglib>
            <taglib-uri>
               http://tomcat.apache.org/debug-taglib
            </taglib-uri>
            <taglib-location>
               /WEB-INF/jsp/debug-taglib.tld
            </taglib-location>
        </taglib>

        <taglib>
            <taglib-uri>
               http://tomcat.apache.org/example-taglib
            </taglib-uri>
            <taglib-location>
               /WEB-INF/jsp/example-taglib.tld
            </taglib-location>
        </taglib>

        <taglib>
            <taglib-uri>
               http://tomcat.apache.org/jsp2-example-taglib
            </taglib-uri>
            <taglib-location>
               /WEB-INF/jsp/jsp2-example-taglib.tld
            </taglib-location>
        </taglib>

        <jsp-property-group>
            <description>
                Special property group for JSP Configuration JSP example.
            </description>
            <display-name>JSPConfiguration</display-name>
            <url-pattern>/jsp/jsp2/misc/config.jsp</url-pattern>
            <el-ignored>true</el-ignored>
            <page-encoding>ISO-8859-1</page-encoding>
            <scripting-invalid>true</scripting-invalid>
            <include-prelude>/jsp/jsp2/misc/prelude.jspf</include-prelude>
            <include-coda>/jsp/jsp2/misc/coda.jspf</include-coda>
        </jsp-property-group>
    </jsp-config>

    <security-constraint>
      <display-name>Example Security Constraint - part 1</display-name>
      <web-resource-collection>
         <web-resource-name>Protected Area - Allow methods</web-resource-name>
         <!-- Define the context-relative URL(s) to be protected -->
         <url-pattern>/jsp/security/protected/*</url-pattern>
         <!-- If you list http methods, only those methods are protected so -->
         <!-- the constraint below ensures all other methods are denied     -->
         <http-method>DELETE</http-method>
         <http-method>GET</http-method>
         <http-method>POST</http-method>
         <http-method>PUT</http-method>
      </web-resource-collection>
      <auth-constraint>
         <!-- Anyone with one of the listed roles may access this area -->
         <role-name>tomcat</role-name>
         <role-name>role1</role-name>
      </auth-constraint>
    </security-constraint>
    <security-constraint>
      <display-name>Example Security Constraint - part 2</display-name>
      <web-resource-collection>
         <web-resource-name>Protected Area - Deny methods</web-resource-name>
         <!-- Define the context-relative URL(s) to be protected -->
         <url-pattern>/jsp/security/protected/*</url-pattern>
         <http-method-omission>DELETE</http-method-omission>
         <http-method-omission>GET</http-method-omission>
         <http-method-omission>POST</http-method-omission>
         <http-method-omission>PUT</http-method-omission>
      </web-resource-collection>
      <!-- An empty auth constraint denies access -->
      <auth-constraint />
    </security-constraint>

    <!-- Default login configuration uses form-based authentication -->
    <login-config>
      <auth-method>FORM</auth-method>
      <realm-name>Example Form-Based Authentication Area</realm-name>
      <form-login-config>
        <form-login-page>/jsp/security/protected/login.jsp</form-login-page>
        <form-error-page>/jsp/security/protected/error.jsp</form-error-page>
      </form-login-config>
    </login-config>

    <!-- Security roles referenced by this web application -->
    <security-role>
      <role-name>role1</role-name>
    </security-role>
    <security-role>
      <role-name>tomcat</role-name>
    </security-role>

    <!-- Environment entry examples -->
    <!--env-entry>
      <env-entry-description>
         The maximum number of tax exemptions allowed to be set.
      </env-entry-description>
      <env-entry-name>maxExemptions</env-entry-name>
      <env-entry-type>java.lang.Integer</env-entry-type>
      <env-entry-value>15</env-entry-value>
    </env-entry-->
    <env-entry>
      <env-entry-name>minExemptions</env-entry-name>
      <env-entry-type>java.lang.Integer</env-entry-type>
      <env-entry-value>1</env-entry-value>
    </env-entry>
    <env-entry>
      <env-entry-name>foo/name1</env-entry-name>
      <env-entry-type>java.lang.String</env-entry-type>
      <env-entry-value>value1</env-entry-value>
    </env-entry>
    <env-entry>
      <env-entry-name>foo/bar/name2</env-entry-name>
      <env-entry-type>java.lang.Boolean</env-entry-type>
      <env-entry-value>true</env-entry-value>
    </env-entry>
    <env-entry>
      <env-entry-name>name3</env-entry-name>
      <env-entry-type>java.lang.Integer</env-entry-type>
      <env-entry-value>1</env-entry-value>
    </env-entry>
    <env-entry>
      <env-entry-name>foo/name4</env-entry-name>
      <env-entry-type>java.lang.Integer</env-entry-type>
      <env-entry-value>10</env-entry-value>
    </env-entry>

    <!-- Async examples -->
    <servlet>
      <servlet-name>async0</servlet-name>
      <servlet-class>async.Async0</servlet-class>
      <async-supported>true</async-supported>
    </servlet>
    <servlet-mapping>
      <servlet-name>async0</servlet-name>
      <url-pattern>/async/async0</url-pattern>
    </servlet-mapping>
    <servlet>
      <servlet-name>async1</servlet-name>
      <servlet-class>async.Async1</servlet-class>
      <async-supported>true</async-supported>
    </servlet>
    <servlet-mapping>
      <servlet-name>async1</servlet-name>
      <url-pattern>/async/async1</url-pattern>
    </servlet-mapping>
    <servlet>
      <servlet-name>async2</servlet-name>
      <servlet-class>async.Async2</servlet-class>
      <async-supported>true</async-supported>
    </servlet>
    <servlet-mapping>
      <servlet-name>async2</servlet-name>
      <url-pattern>/async/async2</url-pattern>
    </servlet-mapping>
    <servlet>
      <servlet-name>async3</servlet-name>
      <servlet-class>async.Async3</servlet-class>
      <async-supported>true</async-supported>
    </servlet>
    <servlet-mapping>
      <servlet-name>async3</servlet-name>
      <url-pattern>/async/async3</url-pattern>
    </servlet-mapping>
    <servlet>
      <servlet-name>stock</servlet-name>
      <servlet-class>async.AsyncStockServlet</servlet-class>
      <async-supported>true</async-supported>
    </servlet>
    <servlet-mapping>
      <servlet-name>stock</servlet-name>
      <url-pattern>/async/stockticker</url-pattern>
    </servlet-mapping>

    <!-- Non-blocking IO examples -->
    <servlet>
      <servlet-name>bytecounter</servlet-name>
      <servlet-class>nonblocking.ByteCounter</servlet-class>
      <async-supported>true</async-supported>
    </servlet>
    <servlet-mapping>
      <servlet-name>bytecounter</servlet-name>
      <url-pattern>/servlets/nonblocking/bytecounter</url-pattern>
    </servlet-mapping>
    <servlet>
      <servlet-name>numberwriter</servlet-name>
      <servlet-class>nonblocking.NumberWriter</servlet-class>
      <async-supported>true</async-supported>
    </servlet>
    <servlet-mapping>
      <servlet-name>numberwriter</servlet-name>
      <url-pattern>/servlets/nonblocking/numberwriter</url-pattern>
    </servlet-mapping>

    <!-- Server Push examples -->
    <servlet>
      <servlet-name>simpleimagepush</servlet-name>
      <servlet-class>http2.SimpleImagePush</servlet-class>
    </servlet>
    <servlet-mapping>
      <servlet-name>simpleimagepush</servlet-name>
      <url-pattern>/servlets/serverpush/simpleimage</url-pattern>
    </servlet-mapping>

    <!-- Trailer examples -->
    <servlet>
      <servlet-name>responsetrailer</servlet-name>
      <servlet-class>trailers.ResponseTrailers</servlet-class>
    </servlet>
    <servlet-mapping>
      <servlet-name>responsetrailer</servlet-name>
      <url-pattern>/servlets/trailers/response</url-pattern>
    </servlet-mapping>

    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
        <welcome-file>index.xhtml</welcome-file>
        <welcome-file>index.htm</welcome-file>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

    <!-- Websocket examples -->
    <listener>
        <listener-class>websocket.drawboard.DrawboardContextListener</listener-class>
    </listener>

    <error-page>
        <error-code>403</error-code>
        <location>/WEB-INF/jsp/403.jsp</location>
    </error-page>

</web-app>
