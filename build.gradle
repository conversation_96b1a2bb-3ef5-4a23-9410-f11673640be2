plugins {
//	id 'base'
	id 'war'
	id 'maven-publish'
	id 'eclipse'
	id 'checkstyle'
	id 'io.github.scm4j.scm4j-releaser-gradle-plugin' version '0.3.0'
	// Frontend (Node.js)
	id 'com.github.node-gradle.node' version '7.1.0'
}

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(8)
	}
}

tasks.withType(JavaCompile).configureEach {
	options.encoding = 'UTF-8'
	sourceCompatibility = JavaVersion.VERSION_1_8
	targetCompatibility = JavaVersion.VERSION_1_8
	options.compilerArgs += ['-Xlint:all', '-Werror']
}

group = 'com.untill'

buildDir = file('build-gradle')

ext.buildDate = new Date()
ext.buildNumber = System.env['BUILD_NUMBER']?.toLong() ?: (long) buildDate.time.intdiv(1000).intdiv(60)

apply from: 'config.gradle'

dependencies {
	providedCompile 'javax.servlet:javax.servlet-api:4.0.1'

	// Test dependencies
	testImplementation 'junit:junit:4.13.2'
	testImplementation 'org.mockito:mockito-core:3.12.4'
}

repositories {
	maven {
		url = untillMavenResolveUrl
		credentials {
			username = untillMavenUsername
			password = untillMavenPassword
		}
	}
}

sourceSets {
	main {
		java.srcDirs = ['src-backend/main/java']
		resources.srcDirs = ['src-backend/main/resources']
	}
	test {
		java.srcDirs = ['src-backend/test/java']
		resources.srcDirs = ['src-backend/test/resources']
	}
}

task eclipseInitPreferences {
	doLast {
		def prefsFile = file('.settings/org.eclipse.core.resources.prefs')
		if (!prefsFile.exists()) {
			prefsFile.parentFile.mkdirs()
			prefsFile.write(
				'eclipse.preferences.version=1\n' +
				'encoding/<project>=UTF-8\n'
			)
		}
	}
}
eclipse {
	classpath {
		downloadJavadoc = true
	}
	project {
		natures 'net.sf.eclipsecs.core.CheckstyleNature'
		buildCommand 'net.sf.eclipsecs.core.CheckstyleBuilder'
	}
	// only for Eclipse Buildship
	synchronizationTasks eclipseInitPreferences
}
// only for eclipse task
tasks.eclipse.dependsOn cleanEclipse
eclipseJdt.dependsOn eclipseInitPreferences

checkstyle {
	toolVersion = '9.3'
	configFile = new File(projectDir, 'checkstyle.xml')
	configProperties['samedir'] = configFile.parent
}

checkstyleTest {
	enabled = false
}

// Frontend (Node.js)
node {
	download = true
}

npm_install {
	//dependsOn npmInstall
	args = ['--no-optional']
    // inputs.file 'package.json'
    // inputs.file 'package-lock.json'
    // outputs.dir 'node_modules'
}

npm_run_build {
	dependsOn npm_install
    // inputs.dir 'src'
    // inputs.file 'package.json'
    // inputs.file 'package-lock.json'
    // outputs.dir 'build'
}

war {
	webAppDirectory = file('src-backend/main/webapp')
	dependsOn npm_run_build
	from 'build'
	manifest.attributes(
			'Specification-Title': project.name,
			'Specification-Version': version.replaceAll(/-SNAPSHOT$/, ''),
			'Implementation-Title': project.name,
			'Implementation-Version': "$version build $buildNumber (${buildDate.format('yyyy-MM-dd')})",
	)
}

test.testLogging.exceptionFormat = 'full'

publish.dependsOn check

publishing {
	publications {
		mavenJava(MavenPublication) {
			from components.web
		}
	}
	repositories {
		maven {
			url = version.endsWith('-SNAPSHOT') ? untillMavenPublishSnapshotUrl : untillMavenPublishUrl
			credentials {
				username = untillMavenUsername
				password = untillMavenPassword
			}
		}
	}
}
