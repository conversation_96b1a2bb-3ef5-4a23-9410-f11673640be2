/* eslint max-lines: off */
import _ from 'lodash';
import {
    ACTION_SWITCH_TAB, ACTION_NEW_TAB, ACTION_DATABASE_OPENED,
    ACTION_DATABASE_JOURNAL_LOADING_ERROR, ACTION_DATABASE_JOURNAL_LOADING_STARTED,
    ACTION_DATABASE_JOURNAL_LOADING_SUCCESS, ACTION_DATABASE_JOURNAL_OPEN_DETAILS,
    ACTION_DATABASE_JOURNAL_CLOSE_DETAILS, ACTION_PERIOD_SWITCH_KIND, ACTION_PERIOD_INC_DAY,
    ACTION_PERIOD_CHANGE_DAY_VALUE, ACTION_PERIOD_CHANGE_RANGE_VALUE, ACTION_FITLERS_CHANGE_VALUE,
    ACTION_FITLERS_CHANGE_VALIDATION, ACTION_DATABASE_CLOSE, ACTION_DATABASE_JOURNAL_DROP_RECORS,
    ACTION_ADD_TAB_FILTER
} from "../actions/Types";

export const DEFAULT_TABS_STATE = {
    active: 'new',
    items: [
        {
            sessionId: 'new',
        },
    ]
};

/* eslint max-statements: "off" */
export default (state = DEFAULT_TABS_STATE, action) => {
    // console.log('TabsReducer, action.type: ', action.type);
    switch (action.type) {
        case ACTION_SWITCH_TAB:
            return {
                ...state,
                active: action.payload.sessionId
            };

        case ACTION_NEW_TAB:
            return {
                ...state,
            };

        case ACTION_DATABASE_OPENED:
            const hhFrom = `0${action.payload.init.workFrom.hours}`.slice(-2);
            const mmFrom = `0${action.payload.init.workFrom.minutes}`.slice(-2);
            const hhTill = `0${action.payload.init.workTill.hours}`.slice(-2);
            const mmTill = `0${action.payload.init.workTill.minutes}`.slice(-2);
            const newItems = state.items.slice();
            const dateFrom = new Date();
            dateFrom.setHours(action.payload.init.workFrom.hours, action.payload.init.workFrom.minutes, 0, 0);
            const dateTill = new Date();
            dateTill.setDate(dateTill.getDate() + 1);

            dateTill.setHours(action.payload.init.workTill.hours, action.payload.init.workTill.minutes, 0, 0);
            newItems.unshift({
                sessionId: action.payload.sessionId,
                title: action.payload.dbname,
                validation: action.payload.validation,
                consistent: action.payload.init.jlogCrcConsistent,
                workingHrs: {
                    from: {
                        hh: action.payload.init.workFrom.hours,
                        mm: action.payload.init.workFrom.minutes,
                    },
                    till: {
                        hh: action.payload.init.workTill.hours,
                        mm: action.payload.init.workTill.minutes,
                    }
                },
                period: {
                    kind: 'day',
                    day: {
                        date: dateFrom,
                        from: `${hhFrom}:${mmFrom}`,
                        till: `${hhTill}:${mmTill}`
                    },
                    range: {
                        from: dateFrom,
                        till: dateTill
                    }
                },
                filters: {
                    kind: "",
                    pcName: "",
                    user: "",
                    transactionNr: "",
                    billNr: "",
                    orderNr: "",
                    hhtIp: ""
                },
                data: {
                    recordsPerPage: 30,
                    hasMore: true,
                    loading: false,
                    loadingError: null,
                    records: [],
                    detailsOpen: false,
                    detailsRecord: -1,
                },
            });

            return {
                ...state,
                items: newItems,
                active: action.payload.sessionId,
            };


        case ACTION_DATABASE_CLOSE:
            let newActive = state.active;
            let idx = -1;

            for (const tab of state.items) {
                if (tab.sessionId === state.active) {
                    idx = state.items.indexOf(tab);
                }
            }

            // console.log('state active ', state.active, 'closng: ', action.payload.sessionId, 'idx: ', idx);
            if (state.active === action.payload.sessionId) {
                if (idx > 0) {
                    newActive = state.items[idx - 1].sessionId;
                } else {
                    newActive = state.items[idx + 1].sessionId;
                }
            }

            const newState = {
                ...state,
                items: state.items.filter((tab) => tab.sessionId !== action.payload.sessionId),
                active: newActive
            };
            // console.log('NEW STATE ', newState);
            return newState;

        case ACTION_DATABASE_JOURNAL_OPEN_DETAILS:
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }

                    const { index } = action.payload;

                    const ticket = index > -1
                        ? tab.data.records[index].ticket
                        : "";
                    const changedValues = index > -1
                        ? tab.data.records[index].changedValues
                        : [];
                    const fields = index > -1
                        ? tab.data.records[index].fields
                        : [];

                    if (ticket.length === 0 && changedValues.length === 0 && Object.keys(fields).length === 0) {
                        // no any details
                        return tab;
                    }

                    return {
                        ...tab,
                        data: {
                            ...tab.data,
                            detailsOpen: true,
                            detailsRecord: action.payload.index
                        }
                    };
                })
            };

        case ACTION_DATABASE_JOURNAL_CLOSE_DETAILS:
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }
                    return {
                        ...tab,
                        data: {
                            ...tab.data,
                            detailsOpen: false
                        }
                    };
                })
            };

        case ACTION_PERIOD_SWITCH_KIND:
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }
                    return {
                        ...tab,
                        period: {
                            ...tab.period,
                            kind: action.payload.kind
                        }
                    };
                })
            };

        case ACTION_PERIOD_INC_DAY:
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }
                    return {
                        ...tab,
                        period: {
                            ...tab.period,
                            day: {
                                ...tab.period.day,
                                date: new Date(tab.period.day.date.getTime() + action.payload.ms)
                            }
                        }
                    };
                })
            };

        case ACTION_FITLERS_CHANGE_VALUE:
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }
                    return {
                        ...tab,
                        filters: {
                            ...tab.filters,
                            [action.payload.key]: action.payload.value
                        }
                    };
                })
            };


        case ACTION_FITLERS_CHANGE_VALIDATION:
            // console.log('ACTION_FITLERS_CHANGE_VALIDATION: ', action.payload.value);
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }
                    return {
                        ...tab,
                        validation: action.payload.value
                    };
                })
            };


        case ACTION_PERIOD_CHANGE_DAY_VALUE:
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }
                    return {
                        ...tab,
                        period: {
                            ...tab.period,
                            day: {
                                ...tab.period.day,
                                [action.payload.key]: action.payload.value
                            }
                        }
                    };
                })
            };

        case ACTION_PERIOD_CHANGE_RANGE_VALUE:
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }
                    return {
                        ...tab,
                        period: {
                            ...tab.period,
                            range: {
                                ...tab.period.range,
                                [action.payload.key]: action.payload.value
                            }
                        }
                    };
                })
            };


        case ACTION_DATABASE_JOURNAL_LOADING_STARTED:
            // console.log('ACTION_DATABASE_JOURNAL_LOADING_STARTED: ', action.payload.sessionId);
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }

                    return {
                        ...tab,
                        data: {
                            ...tab.data,
                            loading: true,
                            loadingError: null
                        }
                    };
                })
            };


        case ACTION_DATABASE_JOURNAL_LOADING_SUCCESS:
            // console.log('ACTION_DATABASE_JOURNAL_LOADING_SUCCESS', action.payload.result);
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }
                    const newRecords = action.payload.append
                        ? tab.data.records.concat(action.payload.result)
                        : action.payload.result;

                    return {
                        ...tab,
                        data: {
                            ...tab.data,
                            detailsRecord: -1,
                            loading: false,
                            loadingError: null,
                            records: newRecords,
                            hasMore: action.payload.result.length === tab.data.recordsPerPage
                        }
                    };
                })
            };

        case ACTION_DATABASE_JOURNAL_LOADING_ERROR:
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }
                    return {
                        ...tab,
                        data: {
                            ...tab.data,
                            loading: false,
                            loadingError: action.payload.error
                        }
                    };
                })
            };

        case ACTION_DATABASE_JOURNAL_DROP_RECORS:
            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }

                    return {
                        ...tab,
                        data: {
                            ...tab.data,
                            records: [], // remove data
                            loading: false,
                            loadingError: action.payload.error
                        }
                    };
                })
            };

        case ACTION_ADD_TAB_FILTER:
            if (!_.isArray(action.payload.filters)) {
                return state;
            }

            //console.log("ACTION_ADD_TAB_FILTER: ", action);

            return {
                ...state,
                items: state.items.map((tab) => {
                    if (tab.sessionId !== action.payload.sessionId) {
                        return tab;
                    }

                    return {
                        ...tab,
                        availableFilters: action.payload.filters,
                    };
                })
            };

        default:
            return state;
    }
};
