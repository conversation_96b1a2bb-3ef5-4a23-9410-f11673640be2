import React, { Component } from 'react'; 
import { connect } from 'react-redux';
import { actionSetLang } from '../actions/MainActions';
import { withCookies } from 'react-cookie';
import fr from '../assets/img/fr.png'
import ge from '../assets/img/ge.png'
import en from '../assets/img/uk.png'
import Menu from '@material-ui/core/Menu';
import MenuItem from '@material-ui/core/MenuItem';
import ListItemText from '@material-ui/core/ListItemText';
import Avatar from '@material-ui/core/Avatar';

class LangSelector extends Component {

    
/*    constructor(props) {
        const { cookies, locale } = this.props;
        if (locale === null) {

        }
//        const locale = cookies.get('jvlocale') || 'en';
super(props);
    } 
*/

    state = {
        anchorEl: null,
    };

    handleClick = event => {
        this.setState({ anchorEl: event.currentTarget });
    };

    handleClose = () => {
        this.setState({ anchorEl: null });
    };

    selectLang(lang) {
        const { cookies, host, port } = this.props;
        cookies.set('jvlocale', lang, {
            maxAge: 3600 * 24 * 365 * 10
        })
        this.setState({ anchorEl: null });
        this.props.actionSetLang(host, port, lang);
    }


    render() {
//        const { cookies } = this.props;
//        const locale = cookies.get('jvlocale') || 'en';
//        console.log('IMG LOCALE: ', this.props.locale);
        if (!this.props.locale || this.props.locale === null) {
            return "";
        }

        let img = en;
        const { anchorEl } = this.state;
        if (this.props.locale === 'fr') {
            img = fr;
        } else if (this.props.locale === 'de') {
            img = ge;
        }
//        console.log('LANG IMG: ', en);
        return (
            <div>
                <img src={img} className="langIcon" onClick={this.handleClick} alt="Language" />
                <Menu
                    id="simple-menu"
                    anchorEl={anchorEl}
                    open={Boolean(anchorEl)}
                    onClose={this.handleClose}
                >
                    <MenuItem onClick={() => { this.selectLang('en') }}>
                        <Avatar alt="English" src={en} /> 
                        <ListItemText primary={`English`} />
                    </MenuItem>
                    <MenuItem onClick={() => { this.selectLang('fr') }}>
                        <Avatar alt="Français" src={fr} /> 
                        <ListItemText primary={`Français`} />
                    </MenuItem>
                    <MenuItem onClick={() => { this.selectLang('de') }}>
                        <Avatar alt="Deutsche" src={ge} /> 
                        <ListItemText primary={`Deutsche`} />
                    </MenuItem>
                </Menu>
            </div>
        );
    }
}

const mapStateToProps = (state) => {
    const { locale, host, port } = state.global;
    return { 
        locale, host, port
    }
};

export default connect(mapStateToProps, 
    { 
        actionSetLang
    })(withCookies(LangSelector));