import { 
    ACTION_NEWCONN_CHANGE_PROP, 
    ACTION_NEWCONN_START_CONNECTING, 
    ACTION_NEWCONN_CONNECT_ERROR, 
    ACTION_NEWCONN_CONNECT_SUCCESS, 
    ACTION_DATABASE_OPENED 
} from "../actions/Types";

function getCurrentPort() {
    const url = new URL(window.location.href);
    let browserPort = parseInt(url.port, 10);
    if (browserPort === 3000) {
        browserPort = 3065;
    }
    return browserPort;
}

function getCurrentHost() {
    const url = new URL(window.location.href);
    return `${url.protocol}//${url.hostname}`;
}

export const DEFAULT_NEWCONN_STATE = {
    host: getCurrentHost(),
    port: getCurrentPort(),
    validation: false,
    connecting: false,
    connectionError: null,
    databases: [],
};

export default (state = DEFAULT_NEWCONN_STATE, action) => {
    switch (action.type) {
        case ACTION_NEWCONN_CHANGE_PROP:
            return {
                ...state,
                [action.payload.key]: action.payload.value
            };

        case ACTION_NEWCONN_START_CONNECTING:
            return {
                ...state,
                connecting: true,
                connectionError: null,
            };

        case ACTION_NEWCONN_CONNECT_ERROR:
            //console.log("ACTION_NEWCONN_CONNECT_ERROR: ", action.payload);

            return {
                ...state,
                connecting: false,
                connectionError: action.payload,
                //databases: []
            };

        case ACTION_NEWCONN_CONNECT_SUCCESS:
            return {
                ...state,
                connecting: false,
                connectionError: null,
                databases: action.payload.items
            };

        case ACTION_DATABASE_OPENED:
            return {
                ...state,
                connecting: false,
                connectionError: null,
            };

        default:
            return state;
    }
};
