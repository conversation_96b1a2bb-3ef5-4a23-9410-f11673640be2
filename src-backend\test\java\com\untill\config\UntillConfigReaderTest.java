package com.untill.config;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

public class UntillConfigReaderTest {

	private String originalUntillHome;
	private Path tempDir;
	private File configFile;

	@Before
	public void setUp() throws IOException {
		// Save original system property
		originalUntillHome = System.getProperty("untill.home");
		
		// Create temporary directory and config file
		tempDir = Files.createTempDirectory("untill-test");
		configFile = new File(tempDir.toFile(), "Untill.ini");
		
		// Set system property to temp directory
		System.setProperty("untill.home", tempDir.toString());
	}

	@After
	public void tearDown() {
		// Restore original system property
		if (originalUntillHome != null) {
			System.setProperty("untill.home", originalUntillHome);
		} else {
			System.clearProperty("untill.home");
		}
		
		// Clean up temp files
		if (configFile != null && configFile.exists()) {
			configFile.delete();
		}
		if (tempDir != null) {
			tempDir.toFile().delete();
		}
	}

	@Test
	public void testReadValidConfig() throws IOException {
		// Create test config file
		try (FileWriter writer = new FileWriter(configFile)) {
			writer.write("[common]\n");
			writer.write("port=6111\n");
			writer.write("debug=true\n");
		}

		UntillConfigReader.UntillConfig config = UntillConfigReader.reloadConfig();
		
		assertNotNull(config);
		assertEquals("localhost", config.getHost());
		assertEquals(6116, config.getPort()); // 6111 + 5
		assertEquals("http://localhost:6116", config.getTargetUrl());
		assertTrue(config.getConfigSource().contains("Untill.ini"));
	}

	@Test
	public void testReadConfigWithDifferentPort() throws IOException {
		// Create test config file with different port
		try (FileWriter writer = new FileWriter(configFile)) {
			writer.write("[common]\n");
			writer.write("port=8000\n");
		}

		UntillConfigReader.UntillConfig config = UntillConfigReader.reloadConfig();
		
		assertNotNull(config);
		assertEquals("localhost", config.getHost());
		assertEquals(8005, config.getPort()); // 8000 + 5
	}

	@Test
	public void testDefaultConfigWhenFileNotExists() {
		// Don't create config file
		UntillConfigReader.UntillConfig config = UntillConfigReader.reloadConfig();
		
		assertNotNull(config);
		assertEquals("localhost", config.getHost());
		assertEquals(6116, config.getPort()); // default
		assertEquals("default", config.getConfigSource());
	}

	@Test
	public void testDefaultConfigWhenUntillHomeNotSet() {
		// Clear system property
		System.clearProperty("untill.home");
		
		UntillConfigReader.UntillConfig config = UntillConfigReader.reloadConfig();
		
		assertNotNull(config);
		assertEquals("localhost", config.getHost());
		assertEquals(6116, config.getPort()); // default
		assertEquals("default", config.getConfigSource());
	}

	@Test
	public void testInvalidPortInConfig() throws IOException {
		// Create test config file with invalid port
		try (FileWriter writer = new FileWriter(configFile)) {
			writer.write("[common]\n");
			writer.write("port=invalid\n");
		}

		UntillConfigReader.UntillConfig config = UntillConfigReader.reloadConfig();
		
		assertNotNull(config);
		assertEquals("localhost", config.getHost());
		assertEquals(6116, config.getPort()); // default fallback
	}
}
