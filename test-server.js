const http = require('http');
const url = require('url');

// Mock users database
const users = {
    'admin': 'admin123',
    'user1': 'password1',
    'test': 'test123'
};

// Function to decode Basic Auth header
function decodeBasicAuth(authHeader) {
    if (!authHeader || !authHeader.startsWith('Basic ')) {
        return null;
    }
    
    const encoded = authHeader.substring(6);
    const decoded = Buffer.from(encoded, 'base64').toString('utf-8');
    const colonIndex = decoded.indexOf(':');
    
    if (colonIndex === -1) {
        return null;
    }
    
    return {
        username: decoded.substring(0, colonIndex),
        password: decoded.substring(colonIndex + 1)
    };
}

// Function to authenticate user
function authenticate(username, password) {
    return users[username] === password;
}

// Create HTTP server
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    console.log(`request parsedUrl.pathname = ${parsedUrl.pathname}`);
    console.log('Access-Control-Request-Headers:', req.headers['access-control-request-headers']);
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
	    res.setHeader('Access-Control-Allow-Origin', '*');
	    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
	    const acrh = req.headers['access-control-request-headers'];
	    console.log('Access-Control-Request-Headers:', acrh);
	    res.setHeader('Access-Control-Allow-Headers', acrh || '*');
	    res.setHeader('Access-Control-Max-Age', '3600');
	    res.writeHead(200);
	    res.end();
	    return;
    }
    
    // Only handle requests to /jv2/UBL/jsapi
    if (!parsedUrl.pathname.includes('/jv2/UBL/jsapi')) {
	    console.log(`!parsedUrl.pathname.includes...`);
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not found' }));
        return;
    }
    
    // Check authentication
    const authHeader = req.headers.authorization;
    const credentials = decodeBasicAuth(authHeader);
    
    if (!credentials || !authenticate(credentials.username, credentials.password)) {
        console.log(`Authentication fail from ${credentials}`);
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
            error: 'Authentication required', 
            message: 'Please provide valid credentials' 
        }));
        return;
    }
    
    // Handle POST requests (JSON-RPC)
    if (req.method === 'POST') {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const jsonRpc = JSON.parse(body);
                console.log(`Authenticated request from ${credentials.username}: ${jsonRpc.method}`);
                
                let result;
                switch (jsonRpc.method) {
                    case 'getDatabases':
                        result = [
                            { name: 'TestDB1', description: 'Test Database 1' },
                            { name: 'TestDB2', description: 'Test Database 2' }
                        ];
                        break;
                    case 'jlogc_getContext2':
                        result = {
                            'en': {
                                'title': 'Journal Viewer',
                                'login': 'Login',
                                'password': 'Password'
                            }
                        };
                        break;
                    case 'openSession':
                        result = 'session_' + Date.now();
                        break;
                    case 'jlog_jlogInit':
                        result = {
                            success: true,
                            sessionId: jsonRpc.params[0]
                        };
                        break;
                    default:
                        result = { success: true, method: jsonRpc.method };
                }
                
                const response = {
                    jsonrpc: '2.0',
                    result: result,
                    id: jsonRpc.id
                };
                
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(response));
                
            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ 
                    jsonrpc: '2.0',
                    error: { code: -32700, message: 'Parse error' },
                    id: null
                }));
            }
        });
    } else {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
    }
});

const PORT = 8081;
server.listen(PORT, () => {
    console.log(`Mock server running on http://localhost:${PORT}`);
    console.log('Available test users:');
    Object.keys(users).forEach(username => {
        console.log(`  ${username} : ${users[username]}`);
    });
    console.log('\nEndpoint: http://localhost:8081/jv2/UBL/jsapi');
});
