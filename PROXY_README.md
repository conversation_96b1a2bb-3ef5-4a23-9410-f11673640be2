# ProxyServlet - HTTP Proxy для ретрансляции запросов

## Описание

ProxyServlet - это HTTP прокси-сервлет, который ретранслирует все входящие запросы на целевой сервер. Он поддерживает все HTTP методы (GET, POST, PUT, DELETE, PATCH и др.) и корректно обрабатывает заголовки и тело запросов/ответов.

## Возможности

- ✅ Поддержка всех HTTP методов
- ✅ Корректная обработка заголовков (исключая hop-by-hop заголовки)
- ✅ Передача тела запроса и ответа
- ✅ Передача query параметров
- ✅ Конфигурация через web.xml
- ✅ Логирование всех операций
- ✅ Обработка ошибок
- 🔄 Готовность к добавлению авторизации

## Конфигурация

### Основной способ - через Untill.ini

ProxyServlet автоматически читает конфигурацию из файла `Untill.ini`:

1. **Системная переменная**: `untill.home` - путь к каталогу с файлом `Untill.ini`
2. **Файл**: `${untill.home}/Untill.ini`
3. **Секция**: `[common]`
4. **Параметр**: `port` - базовый порт Untill
5. **Целевой порт**: `port + 5`
6. **Целевой хост**: `localhost`

#### Пример Untill.ini

```ini
[common]
port=6111
debug=true

[database]
host=localhost
port=5432
```

Результат: ProxyServlet перенаправляет на `http://localhost:6116`

### Альтернативный способ - через web.xml (для обратной совместимости)

```xml
<servlet>
    <servlet-name>ProxyServlet</servlet-name>
    <servlet-class>com.untill.ProxyServlet</servlet-class>
    <init-param>
        <param-name>targetHost</param-name>
        <param-value>localhost</param-value>
    </init-param>
    <init-param>
        <param-name>targetPort</param-name>
        <param-value>6116</param-value>
    </init-param>
</servlet>
<servlet-mapping>
    <servlet-name>ProxyServlet</servlet-name>
    <url-pattern>/proxy/*</url-pattern>
</servlet-mapping>
```

### Приоритет конфигурации

1. Если в web.xml указаны оба параметра (`targetHost` и `targetPort`) - используется web.xml
2. Иначе - читается конфигурация из `Untill.ini`
3. Если файл не найден - используются значения по умолчанию (`localhost:6116`)

## Использование

После развертывания, все запросы к `/proxy/*` будут перенаправлены на целевой сервер.

Примеры:
- `GET /proxy/api/users` → `GET http://*************:6116/proxy/api/users`
- `POST /proxy/api/login` → `POST http://*************:6116/proxy/api/login`

## Логирование

Сервлет использует стандартный Java Logger с именем `com.untill.ProxyServlet`.

Уровни логирования:
- `INFO` - основные операции (инициализация, запросы, ответы)
- `FINE` - детальная информация о заголовках
- `SEVERE` - ошибки

## Безопасность

### Текущее состояние
- Базовая ретрансляция без авторизации
- Фильтрация hop-by-hop заголовков

### Аутентификация (AuthenticatedProxyServlet)

Для добавления авторизации по паролю используйте `AuthenticatedProxyServlet`:

```xml
<servlet>
    <servlet-name>AuthenticatedProxyServlet</servlet-name>
    <servlet-class>com.untill.AuthenticatedProxyServlet</servlet-class>
    <init-param>
        <param-name>authEnabled</param-name>
        <param-value>true</param-value>
    </init-param>
    <init-param>
        <param-name>authUsername</param-name>
        <param-value>admin</param-value>
    </init-param>
    <init-param>
        <param-name>authPassword</param-name>
        <param-value>secure123</param-value>
    </init-param>
</servlet>
```

Клиенты должны использовать Basic Authentication:
```bash
curl -u admin:secure123 http://localhost:8080/app/secure-proxy/api/data
```

### Планируемые улучшения
- Возможность фильтрации определенных заголовков
- Rate limiting
- Логирование безопасности
- Поддержка других методов аутентификации

## Сборка и тестирование

### Сборка проекта
```bash
./gradlew build
```

### Запуск тестов
```bash
./gradlew test
```

### Создание WAR файла
```bash
./gradlew war
```

## Развертывание

1. Соберите WAR файл: `./gradlew war`
2. WAR файл будет создан в `build-gradle/libs/JViewer2-X.X.X-SNAPSHOT.war`
3. Разверните WAR файл в Tomcat (скопируйте в папку `webapps`)
4. Убедитесь, что целевой сервер доступен
5. Проверьте логи для подтверждения инициализации

### Пример развертывания в Tomcat

```bash
# Сборка проекта
./gradlew war

# Копирование в Tomcat (пример для Windows)
copy build-gradle\libs\JViewer2-*.war C:\apache-tomcat\webapps\

# Запуск Tomcat
C:\apache-tomcat\bin\startup.bat
```

После развертывания ProxyServlet будет доступен по адресу:
`http://localhost:8080/JViewer2-X.X.X-SNAPSHOT/proxy/*`

## Мониторинг

Для мониторинга работы прокси рекомендуется:

1. Настроить логирование на уровне INFO
2. Мониторить HTTP статус коды ответов
3. Отслеживать время ответа
4. Проверять доступность целевого сервера

## Устранение неполадок

### Частые проблемы

1. **Connection refused**
   - Проверьте доступность целевого сервера
   - Убедитесь в правильности хоста и порта

2. **Timeout errors**
   - Увеличьте timeout в HttpURLConnection
   - Проверьте сетевую связность

3. **Неправильные заголовки**
   - Проверьте логи на уровне FINE
   - Убедитесь, что hop-by-hop заголовки фильтруются

## Архитектура

```
Client Request → ProxyServlet → Target Server
             ←               ←
Client Response ← ProxyServlet ← Target Server Response
```

ProxyServlet действует как прозрачный прокси, сохраняя все аспекты HTTP запроса и ответа, кроме hop-by-hop заголовков.
