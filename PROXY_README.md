# ProxyServlet - HTTP Proxy для ретрансляции запросов

## Описание

ProxyServlet - это HTTP прокси-сервлет, который ретранслирует все входящие запросы на целевой сервер. Он поддерживает все HTTP методы (GET, POST, PUT, DELETE, PATCH и др.) и корректно обрабатывает заголовки и тело запросов/ответов.

## Возможности

- ✅ Поддержка всех HTTP методов
- ✅ Корректная обработка заголовков (исключая hop-by-hop заголовки)
- ✅ Передача тела запроса и ответа
- ✅ Передача query параметров
- ✅ Логирование всех операций
- ✅ Обработка ошибок
- 🔄 Готовность к добавлению авторизации

## Конфигурация

ProxyServlet автоматически читает конфигурацию из файла `untill.ini`:

1. **Системная переменная**: `untill.home` - путь к каталогу с файлом `untill.ini` (по умолчанию `C:\unTill`)
2. **Файл**: `${untill.home}/untill.ini`
3. **Секция**: `[common]`
4. **Параметр**: `port` - базовый порт unTill (по умолчанию 3060)
5. **Целевой порт (UBL)**: `port + 5`
6. **Целевой хост**: `localhost`

### Пример untill.ini

```ini
[common]
port=6111
```

Результат: ProxyServlet перенаправляет на `http://localhost:6116`

## Использование

После развертывания, все запросы к `/UBL/*` будут перенаправлены на целевой сервер.

## Логирование

Сервлет использует стандартный Java Logger с именем `com.untill.ProxyServlet`.

Уровни логирования:
- `INFO` - основные операции (инициализация, запросы, ответы)
- `FINE` - детальная информация о заголовках
- `SEVERE` - ошибки

# TODO

## Безопасность

### Текущее состояние
- Базовая ретрансляция без авторизации
- Фильтрация hop-by-hop заголовков

### Аутентификация (AuthenticatedProxyServlet)

Для добавления авторизации по паролю используйте `AuthenticatedProxyServlet`:

```xml
<servlet>
    <servlet-name>AuthenticatedProxyServlet</servlet-name>
    <servlet-class>com.untill.AuthenticatedProxyServlet</servlet-class>
    <init-param>
        <param-name>authEnabled</param-name>
        <param-value>true</param-value>
    </init-param>
    <init-param>
        <param-name>authUsername</param-name>
        <param-value>admin</param-value>
    </init-param>
    <init-param>
        <param-name>authPassword</param-name>
        <param-value>secure123</param-value>
    </init-param>
</servlet>
```

Клиенты должны использовать Basic Authentication:
```bash
curl -u admin:secure123 http://localhost:8080/app/secure-proxy/api/data
```

### Планируемые улучшения
- Возможность ограничения доступных функций
- Логирование безопасности
- Поддержка других методов аутентификации

## Сборка и тестирование

### Сборка проекта
```bash
./gradlew build
```

### Запуск тестов
```bash
./gradlew test
```

### Создание WAR файла
```bash
./gradlew war
```

## Развертывание

1. Соберите WAR файл: `./gradlew war`
2. WAR файл будет создан в `build-gradle/libs/JViewer2-X.X.X-SNAPSHOT.war`
3. Разверните WAR файл в Tomcat (скопируйте в папку `webapps`)
4. Передайте системную переменную `untill.home` с путем к каталогу с `untill.ini`, например: `SET JAVA_OPTS=-Duntill.home=/path/to/untill`
5. Убедитесь, что целевой сервер доступен
6. Проверьте логи для подтверждения инициализации

### Пример развертывания в Tomcat

```bash
# Сборка проекта
./gradlew war

# Копирование в Tomcat (пример для Windows)
copy build-gradle\libs\JViewer2-*.war C:\apache-tomcat\webapps\jv2.war

# Запуск Tomcat
SET JAVA_OPTS=-Duntill.home=C:\unTill
C:\apache-tomcat\bin\startup.bat
```

После развертывания ProxyServlet будет доступен по адресу:
`http://localhost:8080/jv2/UBL/*`

## Мониторинг

Для мониторинга работы прокси рекомендуется:

1. Настроить логирование на уровне INFO
2. Мониторить HTTP статус коды ответов
3. Отслеживать время ответа
4. Проверять доступность целевого сервера

## Архитектура

ProxyServlet действует как прозрачный прокси, сохраняя все аспекты HTTP запроса и ответа, кроме hop-by-hop заголовков.
