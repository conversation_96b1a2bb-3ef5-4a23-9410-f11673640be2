<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 (9.0.106) - Host Manager App -- Text Interface</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9</h1><div class="versionInfo">
            Version 9.0.106,
            <time datetime="2025-06-05">Jun 5 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 4.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 2.3 Javadocs</a></li><li><a href="elapi/index.html">EL 3.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 1.1 Javadocs</a></li><li><a href="jaspicapi/index.html">JASPIC 1.1 Javadocs</a></li><li><a href="annotationapi/index.html">Common Annotations 1.3 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Host Manager App -- Text Interface</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
  <ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Configuring_Manager_Application_Access">Configuring Manager Application Access</a></li><li><a href="#List_of_Commands">List of Commands</a><ol><li><a href="#List_command">List command</a></li><li><a href="#Add_command">Add command</a></li><li><a href="#Remove_command">Remove command</a></li><li><a href="#Start_command">Start command</a></li><li><a href="#Stop_command">Stop command</a></li><li><a href="#Persist_command">Persist command</a></li></ol></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">
  <p>
    The <strong>Tomcat Host Manager</strong> application enables you to create,
    delete, and otherwise manage virtual hosts within Tomcat. This how-to guide
    is best accompanied by the following pieces of documentation:
  </p>
  <ul>
    <li>
      <a href="virtual-hosting-howto.html">Virtual Hosting How-To</a> for more
      information about virtual hosting.
    </li>
    <li>
      <a href="config/host.html">The Host Container</a> for more information
      about the underlying xml configuration of virtual hosts and description
      of attributes.
    </li>
  </ul>

  <p>
    The <strong>Tomcat Host Manager</strong> application is a part of
    Tomcat installation, by default available using the following
    context: <code>/host-manager</code>. You can use the host manager in the
    following ways:
  </p>

  <ul>
    <li>
      Utilizing the graphical user interface, accessible at:
      <code>{server}:{port}/host-manager/html</code>.
    </li>
    <li>
      Utilizing a set of minimal HTTP requests suitable for scripting.
      You can access this mode at:
      <code>{server}:{port}/host-manager/text</code>.
    </li>
  </ul>
  <p>
    Both ways enable you to add, remove, start, and stop virtual hosts. Changes
    may be persisted by using the <code>persist</code> command. This document
    focuses on the text interface. For further information about the graphical
    interface, see
    <a href="html-host-manager-howto.html">Host Manager App -- HTML Interface</a>.
  </p>
</div><h3 id="Configuring_Manager_Application_Access">Configuring Manager Application Access</h3><div class="text">
  <p><em>The description below uses <code>$CATALINA_HOME</code> to refer the
    base Tomcat directory. It is the directory in which you installed
    Tomcat, for example <code>C:\tomcat9</code>, or
    <code>/usr/share/tomcat9</code>.</em></p>

  <p>
    The Host Manager application requires a user with one of the following
    roles:
  </p>

  <ul>
    <li>
      <code>admin-gui</code> - use this role for the graphical web interface.
    </li>
    <li>
      <code>admin-script</code> - use this role for the scripting web interface.
    </li>
  </ul>

  <p>
    To enable access to the text interface of the Host Manager application,
    either grant your Tomcat user the appropriate role, or create a new one with
    the correct role. For example, open
    <code>${CATALINA_BASE}/conf/tomcat-users.xml</code> and enter the following:
  </p>
  <div class="codeBox"><pre><code>&lt;user username="test" password="chang3m3N#w" roles="admin-script"/&gt;</code></pre></div>
  <p>
    No further settings is needed. When you now access
    <code>{server}:{port}/host-manager/text/${COMMAND}</code>,you are able to
    log in with the created credentials. For example:
    <div class="codeBox"><pre><code>$ curl -u ${USERNAME}:${PASSWORD} http://localhost:8080/host-manager/text/list
OK - Listed hosts
localhost:</code></pre></div>
  </p>
  <p>
    If you are using a different realm you will need to add the necessary role
    to the appropriate user(s) using the standard user management tools for that
    realm.
  </p>
</div><h3 id="List_of_Commands">List of Commands</h3><div class="text">
  <p>The following commands are supported:</p>
  <ul>
    <li>list</li>
    <li>add</li>
    <li>remove</li>
    <li>start</li>
    <li>stop</li>
    <li>persist</li>
  </ul>
  <p>
    In the following subsections, the username and password is assumed to be
    <b>test:test</b>. For your environment, use credentials created in the
    previous sections.
  </p>
  <div class="subsection"><h4 id="List_command">List command</h4><div class="text">
    <p>
      Use the <b>list</b> command to see the available virtual hosts on your
      Tomcat instance.
    </p>
    <p><i>Example command</i>:</p>
    <code>curl -u test:test http://localhost:8080/host-manager/text/list</code>
    <p><i>Example response</i>:</p>
    <div class="codeBox"><pre><code>OK - Listed hosts
localhost:</code></pre></div>
  </div></div>
  <div class="subsection"><h4 id="Add_command">Add command</h4><div class="text">
    <p>
      Use the <b>add</b> command to add a new virtual host. Parameters used
      for the <b>add</b> command:
    </p>
    <ul>
      <li>String <b>name</b>: Name of the virtual host. <b>REQUIRED</b></li>
      <li>String <b>aliases</b>: Aliases for your virtual host.</li>
      <li>String <b>appBase</b>: Base path for the application that will be
      served by this virtual host. Provide relative or absolute path.</li>
      <li>Boolean <b>manager</b>: If true, the Manager app is added to the
      virtual host. You can access it with the <i>/manager</i> context.</li>
      <li>Boolean <b>autoDeploy</b>: If true, Tomcat automatically redeploys
      applications placed in the appBase directory.</li>
      <li>Boolean <b>deployOnStartup</b>: If true, Tomcat automatically deploys
      applications placed in the appBase directory on startup.</li>
      <li>Boolean <b>deployXML</b>: If true, the <i>/META-INF/context.xml</i>
      file is read and used by Tomcat.</li>
      <li>Boolean <b>copyXML</b>: If true, Tomcat copies <i>/META-INF/context.xml</i>
      file and uses the original copy regardless of updates to the application's
      <i>/META-INF/context.xml</i> file.</li>
    </ul>
    <p><i>Example command</i>:</p>
    <div class="codeBox"><pre><code>curl -u test:test http://localhost:8080/host-manager/text/add?name=www.awesomeserver.com&amp;aliases=awesomeserver.com&amp;appBase/mnt/appDir&amp;deployOnStartup=true</code></pre></div>
    <p><i>Example response</i>:</p>
    <div class="codeBox"><pre><code>add: Adding host [www.awesomeserver.com]</code></pre></div>
  </div></div>
  <div class="subsection"><h4 id="Remove_command">Remove command</h4><div class="text">
    <p>
      Use the <b>remove</b> command to remove a virtual host. Parameters used
      for the <b>remove</b> command:
    </p>
    <ul>
      <li>String <b>name</b>: Name of the virtual host to be removed.
      <b>REQUIRED</b></li>
    </ul>
    <p><i>Example command</i>:</p>
    <div class="codeBox"><pre><code>curl -u test:test http://localhost:8080/host-manager/text/remove?name=www.awesomeserver.com</code></pre></div>
    <p><i>Example response</i>:</p>
    <div class="codeBox"><pre><code>remove: Removing host [www.awesomeserver.com]</code></pre></div>
  </div></div>
  <div class="subsection"><h4 id="Start_command">Start command</h4><div class="text">
    <p>
      Use the <b>start</b> command to start a virtual host. Parameters used
      for the <b>start</b> command:
    </p>
    <ul>
      <li>String <b>name</b>: Name of the virtual host to be started.
      <b>REQUIRED</b></li>
    </ul>
    <p><i>Example command</i>:</p>
    <div class="codeBox"><pre><code>curl -u test:test http://localhost:8080/host-manager/text/start?name=www.awesomeserver.com</code></pre></div>
    <p><i>Example response</i>:</p>
    <div class="codeBox"><pre><code>OK - Host www.awesomeserver.com started</code></pre></div>
  </div></div>
  <div class="subsection"><h4 id="Stop_command">Stop command</h4><div class="text">
    <p>
      Use the <b>stop</b> command to stop a virtual host. Parameters used
      for the <b>stop</b> command:
    </p>
    <ul>
      <li>String <b>name</b>: Name of the virtual host to be stopped.
      <b>REQUIRED</b></li>
    </ul>
    <p><i>Example command</i>:</p>
    <div class="codeBox"><pre><code>curl -u test:test http://localhost:8080/host-manager/text/stop?name=www.awesomeserver.com</code></pre></div>
    <p><i>Example response</i>:</p>
    <div class="codeBox"><pre><code>OK - Host www.awesomeserver.com stopped</code></pre></div>
  </div></div>
  <div class="subsection"><h4 id="Persist_command">Persist command</h4><div class="text">
    <p>
      Use the <b>persist</b> command to persist a virtual host into
      <b>server.xml</b>. Parameters used for the <b>persist</b> command:
    </p>
    <ul>
      <li>String <b>name</b>: Name of the virtual host to be persist.
      <b>REQUIRED</b></li>
    </ul>
    <p>
      This functionality is disabled by default. To enable this option, you must
      configure the <code>StoreConfigLifecycleListener</code> listener first.
      To do so, add the following listener to your <i>server.xml</i>:
    </p>
    <div class="codeBox"><pre><code>&lt;Listener className="org.apache.catalina.storeconfig.StoreConfigLifecycleListener"/&gt;</code></pre></div>
    <p><i>Example command</i>:</p>
    <div class="codeBox"><pre><code>curl -u test:test http://localhost:8080/host-manager/text/persist?name=www.awesomeserver.com</code></pre></div>
    <p><i>Example response</i>:</p>
    <div class="codeBox"><pre><code>OK - Configuration persisted</code></pre></div>
    <p><i>Example manual entry</i>:</p>
    <div class="codeBox"><pre><code>&lt;Host appBase="www.awesomeserver.com" name="www.awesomeserver.com" deployXML="false" unpackWARs="false"&gt;
&lt;/Host&gt;</code></pre></div>
  </div></div>
</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>