<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 (9.0.106) - Jasper 2 JSP Engine How To</title><meta name="author" content="Glenn <PERSON>"><meta name="author" content="<PERSON>"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9</h1><div class="versionInfo">
            Version 9.0.106,
            <time datetime="2025-06-05">Jun 5 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 4.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 2.3 Javadocs</a></li><li><a href="elapi/index.html">EL 3.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 1.1 Javadocs</a></li><li><a href="jaspicapi/index.html">JASPIC 1.1 Javadocs</a></li><li><a href="annotationapi/index.html">Common Annotations 1.3 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Jasper 2 JSP Engine How To</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Configuration">Configuration</a></li><li><a href="#Known_issues">Known issues</a></li><li><a href="#Production_Configuration">Production Configuration</a></li><li><a href="#Web_Application_Compilation">Web Application Compilation</a></li><li><a href="#Optimisation">Optimisation</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

<p>Tomcat 9.0 uses the Jasper 2 JSP Engine to implement
the <a href="https://cwiki.apache.org/confluence/display/TOMCAT/Specifications">JavaServer Pages 2.3</a>
specification.</p>

<p>Jasper 2 has been redesigned to significantly improve performance over
the original Jasper.  In addition to general code improvements the following
changes were made:</p>
<ul>
<li><strong>JSP Custom Tag Pooling</strong> - The java objects instantiated
for JSP Custom Tags can now be pooled and reused.  This significantly boosts
the performance of JSP pages which use custom tags.</li>
<li><strong>Background JSP compilation</strong> - If you make a change to
a JSP page which had already been compiled Jasper 2 can recompile that
page in the background.  The previously compiled JSP page will still be
available to serve requests.  Once the new page has been compiled
successfully it will replace the old page.  This helps improve availability
of your JSP pages on a production server.</li>
<li><strong>Recompile JSP when included page changes</strong> - Jasper 2
can now detect when a page included at compile time from a JSP has changed
and then recompile the parent JSP.</li>
<li><strong>JDT used to compile JSP pages</strong> - The
Eclipse JDT Java compiler is now used to perform JSP java source code
compilation. This compiler loads source dependencies from the container
classloader. Ant and javac can still be used.</li>
</ul>


<p>Jasper is implemented using the servlet class
<code>org.apache.jasper.servlet.JspServlet</code>.</p>

</div><h3 id="Configuration">Configuration</h3><div class="text">

<p>By default Jasper is configured for use when doing web application
development.  See the section <a href="#Production_Configuration">
Production Configuration</a> for information on configuring Jasper
for use on a production Tomcat server.</p>

<p>The servlet which implements Jasper is configured using init parameters
in your global <code>$CATALINA_BASE/conf/web.xml</code>.
</p>
<ul>
<li><strong>checkInterval</strong> - If development is false and checkInterval
is greater than zero, background compiles are enabled. checkInterval is the time
in seconds between checks to see if a JSP page (and its dependent files) needs
to be recompiled. Default <code>0</code> seconds.</li>

<li><strong>classdebuginfo</strong> - Should the class file be compiled with
debugging information?  <code>true</code> or <code>false</code>, default
<code>true</code>.
</li>

<li><strong>classpath</strong> - Defines the class path to be used to compile
the generated servlets. This parameter only has an effect if the ServletContext
attribute org.apache.jasper.Constants.SERVLET_CLASSPATH is not set. This
attribute is always set when Jasper is used within Tomcat. By default the
classpath is created dynamically based on the current web application.</li>

<li><strong>compiler</strong> - Which compiler Ant should use to compile JSP
pages. The valid values for this are the same as for the compiler attribute of
Ant's
<a href="https://ant.apache.org/manual/Tasks/javac.html#compilervalues">javac</a>
task. If the value is not set, then the default Eclipse JDT Java compiler will
be used instead of using Ant. There is no default value. If this attribute is
set then <code>setenv.[sh|bat]</code> should be used to add
<code>ant.jar</code>, <code>ant-launcher.jar</code> and <code>tools.jar</code>
to the <code>CLASSPATH</code> environment variable.</li>

<li><strong>compilerSourceVM</strong> - What JDK version are the source files
compatible with? (Default value: <code>1.8</code>)</li>

<li><strong>compilerTargetVM</strong> - What JDK version are the generated files
compatible with? (Default value: <code>1.8</code>)</li>

<li><strong>development</strong> - Is Jasper used in development mode? If true,
the frequency at which JSPs are checked for modification may be specified via
the modificationTestInterval parameter.<code>true</code> or <code>false</code>,
default <code>true</code>.</li>

<li><strong>displaySourceFragment</strong> - Should a source fragment be
included in exception messages? <code>true</code> or <code>false</code>,
default <code>true</code>.</li>

<li><strong>dumpSmap</strong> - Should the SMAP info for JSR45 debugging be
dumped to a file? <code>true</code> or <code>false</code>, default
<code>false</code>. <code>false</code> if suppressSmap is true.</li>

<li><strong>enablePooling</strong> - Determines whether tag handler pooling is
enabled. This is a compilation option. It will not alter the behaviour of JSPs
that have already been compiled. <code>true</code> or <code>false</code>,
default <code>true</code>.
</li>

<li><strong>engineOptionsClass</strong> - Allows specifying the Options class
used to configure Jasper. If not present, the default EmbeddedServletOptions
will be used. This option is ignored if running under a SecurityManager.
</li>

<li><strong>errorOnUseBeanInvalidClassAttribute</strong> - Should Jasper issue
an error when the value of the class attribute in an useBean action is not a
valid bean class? <code>true</code> or <code>false</code>, default
<code>true</code>.</li>

<li><strong>fork</strong> - Have Ant fork JSP page compiles so they are
performed in a separate JVM from Tomcat? <code>true</code> or
<code>false</code>, default <code>true</code>.</li>

<li><strong>genStringAsCharArray</strong> - Should text strings be generated as char
arrays, to improve performance in some cases? Default <code>false</code>.</li>

<li><strong>ieClassId</strong> - Deprecated. Will be removed in Tomact 10.1.
The class-id value to be sent to Internet
Explorer when using &lt;jsp:plugin&gt; tags.   Default
<code>clsid:8AD9C840-044E-11D1-B3E9-00805F499D93</code>.</li>

<li><strong>javaEncoding</strong> - Java file encoding to use for generating
java source files. Default <code>UTF8</code>.</li>

<li><strong>keepgenerated</strong> - Should we keep the generated Java source
code for each page instead of deleting it? <code>true</code> or
<code>false</code>, default <code>true</code>.</li>

<li><strong>mappedfile</strong> - Should we generate static content with one
print statement per input line, to ease debugging?
<code>true</code> or <code>false</code>, default <code>true</code>.</li>

<li><strong>maxLoadedJsps</strong> - The maximum number of JSPs that will be
loaded for a web application. If more than this number of JSPs are loaded, the
least recently used JSPs will be unloaded so that the number of JSPs loaded at
any one time does not exceed this limit. A value of zero or less indicates no
limit. Default <code>-1</code></li>

<li><strong>jspIdleTimeout</strong> - The amount of time in seconds a JSP can be
idle before it is unloaded. A value of zero or less indicates never unload.
Default <code>-1</code></li>

<li><strong>modificationTestInterval</strong> - Causes a JSP (and its dependent
files) to not be checked for modification during the specified time interval
(in seconds) from the last time the JSP was checked for modification. A value of
0 will cause the JSP to be checked on every access. Used in development mode
only. Default is <code>4</code> seconds.</li>

<li><strong>recompileOnFail</strong> - If a JSP compilation fails should the
modificationTestInterval be ignored and the next access trigger a re-compilation
attempt? Used in development mode only and is disabled by default as compilation
may be expensive and could lead to excessive resource usage.</li>

<li><strong>scratchdir</strong> - What scratch directory should we use when
compiling JSP pages? Default is the work directory for the current web
application. This option is ignored if running under a SecurityManager.</li>

<li><strong>suppressSmap</strong> - Should the generation of SMAP info for JSR45
debugging be suppressed? <code>true</code> or <code>false</code>, default
<code>false</code>.</li>

<li><strong>trimSpaces</strong> - Should template text that consists entirely of
whitespace be removed from the output (<code>true</code>), replaced with a
single space (<code>single</code>) or left unchanged (<code>false</code>)?
Alternatively, the <code>extended</code> option will remove leading and trailing
whitespace from template text and collapse sequences of whitespace and newlines
within the template text to a single new line. Note that if a JSP page or tag
file specifies a <code>trimDirectiveWhitespaces</code> value of
<code>true</code>, that will take precedence over this configuration setting for
that page/tag. Default <code>false</code>.</li>

<li><strong>xpoweredBy</strong> - Determines whether X-Powered-By response
header is added by generated servlet. <code>true</code> or <code>false</code>,
default <code>false</code>.</li>

<li><strong>strictQuoteEscaping</strong> - When scriptlet expressions are used
for attribute values, should the rules in JSP.1.6 for the escaping of quote
characters be strictly applied? <code>true</code> or <code>false</code>, default
<code>true</code>.</li>

<li><strong>quoteAttributeEL</strong> - When EL is used in an attribute value
on a JSP page, should the rules for quoting of attributes described in JSP.1.6
be applied to the expression? <code>true</code> or <code>false</code>, default
<code>true</code>.</li>
</ul>

<p>The Java compiler from Eclipse JDT in included as the default compiler. It is
an advanced Java compiler which will load all dependencies from the Tomcat class
loader, which will help tremendously when compiling on large installations with
tens of JARs. On fast servers, this will allow sub-second recompilation cycles
for even large JSP  pages.</p>

<p>Apache Ant, which was used in previous Tomcat releases, can be used instead
of the new compiler by configuring the compiler attribute as explained above.
</p>

<p>If you need to change the JSP Servlet settings for an application you can
override the default configuration by re-defining the JSP Servlet in
<code>/WEB-INF/web.xml</code>. However, this may cause problems if you attempt
to deploy the application on another container as the JSP Servlet class may
not be recognised. You can work-around this problem by using the Tomcat specific
<code>/WEB-INF/tomcat-web.xml</code> deployment descriptor. The format is
identical to <code>/WEB-INF/web.xml</code>. It will override any default
settings but not those in <code>/WEB-INF/web.xml</code>. Since it is Tomcat
specific, it will only be processed when the application is deployed on
Tomcat.</p>

</div><h3 id="Known_issues">Known issues</h3><div class="text">

<p>As described in
<a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=39089">
bug 39089</a>, a known JVM issue,
<a href="http://bugs.sun.com/bugdatabase/view_bug.do?bug_id=6294277">
bug 6294277</a>, may cause a
<code>java.lang.InternalError: name is too long to represent</code> exception
when compiling very large JSPs. If this is observed then it may be worked around
by using one of the following:
</p>
<ul>
<li>reduce the size of the JSP</li>
<li>disable SMAP generation and JSR-045 support by setting
<code>suppressSmap</code> to <code>true</code>.</li>
</ul>

</div><h3 id="Production_Configuration">Production Configuration</h3><div class="text">

<p>The main JSP optimization which can be done is precompilation of JSPs.
However, this might not be possible (for example, when using the
jsp-property-group feature) or practical, in which case the configuration of the
Jasper servlet becomes critical.</p>

<p>When using Jasper 2 in a production Tomcat server you should consider making
the following changes from the default configuration.</p>
<ul>
<li><strong>development</strong> - To disable on access checks for JSP
pages compilation set this to <code>false</code>.</li>
<li><strong>genStringAsCharArray</strong> - To generate slightly more efficient
char arrays, set this to <code>true</code>.</li>
<li><strong>modificationTestInterval</strong> - If development has to be set to
<code>true</code> for any reason (such as dynamic generation of JSPs), setting
this to a high value will improve performance a lot.</li>
<li><strong>trimSpaces</strong> - To remove unnecessary bytes from the response,
consider setting this to <code>single</code> or <code>extended</code>.</li>
</ul>

</div><h3 id="Web_Application_Compilation">Web Application Compilation</h3><div class="text">

<p>Using Ant is the preferred way to compile web applications using JSPC. Note
that when pre-compiling JSPs, SMAP information will only be included in the
final classes if suppressSmap is false and compile is true.
Use the script given below (a similar script is included in the "deployer"
download) to precompile a webapp:
</p>

<div class="codeBox"><pre><code>&lt;project name="Webapp Precompilation" default="all" basedir="."&gt;

   &lt;import file="${tomcat.home}/bin/catalina-tasks.xml"/&gt;

   &lt;target name="jspc"&gt;

    &lt;jasper
             validateXml="false"
             uriroot="${webapp.path}"
             webXmlInclude="${webapp.path}/WEB-INF/generated_web.xml"
             outputDir="${webapp.path}/WEB-INF/src" /&gt;

  &lt;/target&gt;

  &lt;target name="compile"&gt;

    &lt;mkdir dir="${webapp.path}/WEB-INF/classes"/&gt;
    &lt;mkdir dir="${webapp.path}/WEB-INF/lib"/&gt;

    &lt;javac destdir="${webapp.path}/WEB-INF/classes"
           debug="on" failonerror="false"
           srcdir="${webapp.path}/WEB-INF/src"
           excludes="**/*.smap"&gt;
      &lt;classpath&gt;
        &lt;pathelement location="${webapp.path}/WEB-INF/classes"/&gt;
        &lt;fileset dir="${webapp.path}/WEB-INF/lib"&gt;
          &lt;include name="*.jar"/&gt;
        &lt;/fileset&gt;
        &lt;pathelement location="${tomcat.home}/lib"/&gt;
        &lt;fileset dir="${tomcat.home}/lib"&gt;
          &lt;include name="*.jar"/&gt;
        &lt;/fileset&gt;
        &lt;fileset dir="${tomcat.home}/bin"&gt;
          &lt;include name="*.jar"/&gt;
        &lt;/fileset&gt;
      &lt;/classpath&gt;
      &lt;include name="**" /&gt;
      &lt;exclude name="tags/**" /&gt;
    &lt;/javac&gt;

  &lt;/target&gt;

  &lt;target name="all" depends="jspc,compile"&gt;
  &lt;/target&gt;

  &lt;target name="cleanup"&gt;
    &lt;delete&gt;
        &lt;fileset dir="${webapp.path}/WEB-INF/src"/&gt;
        &lt;fileset dir="${webapp.path}/WEB-INF/classes/org/apache/jsp"/&gt;
    &lt;/delete&gt;
  &lt;/target&gt;

&lt;/project&gt;</code></pre></div>

<p>
The following command line can be used to run the script
(replacing the tokens with the Tomcat base path and the path to the webapp
which should be precompiled):
</p>
<div class="codeBox"><pre><code>$ANT_HOME/bin/ant -Dtomcat.home=&lt;$TOMCAT_HOME&gt; -Dwebapp.path=&lt;$WEBAPP_PATH&gt;</code></pre></div>


<p>
Then, the declarations and mappings for the servlets which were generated
during the precompilation must be added to the web application deployment
descriptor. Insert the <code>${webapp.path}/WEB-INF/generated_web.xml</code>
at the right place inside the <code>${webapp.path}/WEB-INF/web.xml</code> file.
Restart the web application (using the manager) and test it to verify it is
running fine with precompiled servlets. An appropriate token placed in the
web application deployment descriptor may also be used to automatically
insert the generated servlet declarations and mappings using Ant filtering
capabilities. This is actually how all the webapps distributed with Tomcat
are automatically compiled as part of the build process.
</p>

<p>
At the jasper task you can use the option <code>addWebXmlMappings</code> for
automatic merge the <code>${webapp.path}/WEB-INF/generated_web.xml</code>
with the current web application deployment descriptor at
<code>${webapp.path}/WEB-INF/web.xml</code>.
</p>

<p>
When you want to use a specific version of Java for your JSP's, add the
javac compiler task attributes <code>source</code> and <code>target</code> with
appropriate values. For example, <code>16</code> to compile JSPs for Java 16.
</p>

<p>
For production you may wish to disable debug info with
<code>debug="off"</code>.
</p>

<p>
When you don't want to stop the JSP generation at first JSP syntax error, use
<code>failOnError="false"</code> and with
<code>showSuccess="true"</code> all successful <i>JSP to Java</i>
generation are printed out. Sometimes it is very helpful, when you cleanup the
generate java source files at <code>${webapp.path}/WEB-INF/src</code>
and the compile JSP servlet classes at
<code>${webapp.path}/WEB-INF/classes/org/apache/jsp</code>.
</p>

<p><strong>Hints:</strong></p>
<ul>
<li> When you switch to another Tomcat release, then regenerate and recompile
your JSP's with the new Tomcat version.</li>
<li>Use java system property at server runtime to disable PageContext pooling
<code>org.apache.jasper.runtime.JspFactoryImpl.USE_POOL=false</code>.
and limit the buffering with
<code>org.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER=true</code>. Note
that changing from the defaults may affect performance, but it will vary
depending on the application.</li>
</ul>
</div><h3 id="Optimisation">Optimisation</h3><div class="text">
<p>
There are a number of extension points provided within Jasper that enable the
user to optimise the behaviour for their environment.
</p>

<p>
The first of these extension points is the tag plug-in mechanism. This allows
alternative implementations of tag handlers to be provided for a web application
to use. Tag plug-ins are registered via a <code>tagPlugins.xml</code> file
located under <code>WEB-INF</code>. A sample plug-in for the JSTL is included
with Jasper.
</p>

<p>
The second extension point is the Expression Language interpreter. Alternative
interpreters may be configured through the <code>ServletContext</code>. See the
<code>ELInterpreterFactory</code> javadoc for details of how to configure an
alternative EL interpreter. A alternative interpreter primarily targeting tag
settings is provided at
<code>org.apache.jasper.optimizations.ELInterpreterTagSetters</code>. See the
javadoc for details of the optimisations and the impact they have on
specification compliance.
</p>

<p>
An extension point is also provided for coercion of String values to Enums. It
is provided at
<code>org.apache.jasper.optimizations.StringInterpreterEnum</code>. See the
javadoc for details of the optimisations and the impact they have on
specification compliance.
</p>
</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>