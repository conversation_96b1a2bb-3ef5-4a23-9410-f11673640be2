import _ from 'lodash';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import { connect } from 'react-redux';
import { actionNewConnectionChangeProp, actionConnectToServer, actionOpenDatabase } from '../actions/MainActions';
import TextField from '@material-ui/core/TextField';
import Button from '@material-ui/core/Button';
import CircularProgress from '@material-ui/core/CircularProgress';
import List from '@material-ui/core/List';
import ListItem from '@material-ui/core/ListItem';
import ListItemIcon from '@material-ui/core/ListItemIcon';
import ListItemText from '@material-ui/core/ListItemText';
import Icon from '@material-ui/core/Icon';
import Checkbox from '@material-ui/core/Checkbox';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import { withCookies } from 'react-cookie';
import { rc } from '../lib/utils';

import { notification } from 'antd';


const ErrorNotificationKey = "errorNotificationCnt";
const styles = theme => ({
    container: {
        display: 'flex',
        flexWrap: 'wrap',
    },
    textField: {
        marginLeft: theme.spacing.unit,
        marginRight: theme.spacing.unit,
        width: 200,
    },
    menu: {
        width: 200,
    },
    button: {
        margin: theme.spacing.unit,
    },
    input: {
        display: 'none',
    },
    progress: {
        margin: theme.spacing.unit * 2,
    },
});

class NewConnection extends Component {
    componentDidMount() {
        this.connectToServer();
    }

    componentDidUpdate(oldProps) {
        if (!_.isNil(this.props.connectionError) && this.props.connectionError !== oldProps.connectionError) {
            this.showConnectionError();
        }
    }

    connectToServer() {
        const { cookies } = this.props;
        const cookieLocale = cookies.get('jvlocale');

        this.props.actionConnectToServer(this.props.host, this.props.port, this.props.locale, cookieLocale);
    }

    showConnectionError() {
        const { connectionError } = this.props;

        notification.error({
            key: ErrorNotificationKey,
            message: rc(this.props.strings, 'connectingErrorTitle'),
            description: connectionError,
            duration: null,
            btn: (
                <Button
                    onClick={() => {
                        this.connectToServer();
                        notification.close(ErrorNotificationKey)
                    }}
                >
                    {rc(this.props.strings, 'reconnect')}
                </Button>
            )
        });
    }

    renderConnectionError() {
        if (this.props.connectionError && this.props.connectionError.length > 0) {

            return (
                <div className="error">{this.props.connectionError}</div>
            );
        }

        return null;
    }

    selectDatabase(dbname) {
        this.props.actionOpenDatabase({
            host: this.props.host,
            port: this.props.port,
            dbname,
            validation: this.props.validation
        });
    }

    renderList() {
        if ((this.props.databases || []).length === 0) {
            return (
                <div />
            );
        }

        const items = [];
        let key = 0;
        for (const item of this.props.databases) {
            items.push(
                <ListItem
                    key={key++}
                    button
                    onClick={
                        () => { this.selectDatabase(item) }
                    }
                >
                    <ListItemIcon>
                        <Icon>folder</Icon>
                    </ListItemIcon>
                    <ListItemText primary={item} />
                </ListItem>
            );
        }

        return (
            <div>
                <h2>{rc(this.props.strings, 'options')}</h2>
                <div>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={this.props.validation}
                                onChange={(event) => {
                                    this.props.actionNewConnectionChangeProp('validation', event.target.checked)
                                }}
                                value="checkedA"
                            />
                        }
                        label={rc(this.props.strings, 'enableIntegrityCheck')}
                    />
                </div>
                <h2>{rc(this.props.strings, 'chooseDatabase')}</h2>
                <List component="nav">
                    {items}
                </List>
            </div>
        );
    }

    renderConnectForm() {
        const { classes } = this.props;

        if (this.props.connected) {
            return null;
        }

        return (
            <div style={{ maxWidth: '430pt' }}>
                <h2>unTill JServer</h2>
                <form className={classes.container} noValidate autoComplete="off">
                    <TextField
                        id="name"
                        label="Host"
                        className={classes.textField}
                        value={this.props.host}
                        onChange={(event) => {
                            this.props.actionNewConnectionChangeProp('host', event.target.value)
                        }}
                        margin="normal"
                    />
                    <TextField
                        id="number"
                        label="Port"
                        value={this.props.port}
                        onChange={(event) => {
                            this.props.actionNewConnectionChangeProp('port', event.target.value)
                        }}
                        type="number"
                        className={classes.textField}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        margin="normal"
                    />
                    <Button
                        variant="contained"
                        className={classes.button}
                        onClick={() => {
                            this.props.actionConnectToServer(this.props.host, this.props.port, this.props.locale);
                        }}
                    >
                        Reconnect
                    </Button>
                </form>
                { this.renderConnectionError()}
            </div>
        );
    }

    render() {
        const { classes } = this.props;

        if (this.props.connecting) {
            return (
                <CircularProgress className={classes.progress} />
            );
        }

        return (
            <div id="new_connection">
                {this.renderConnectForm()}
                {this.renderList()}
            </div>
        )
    }
}

const mapStateToProps = (state) => ({
    port: state.newConnection.port,
    host: state.newConnection.host,
    validation: state.newConnection.validation,
    connecting: state.newConnection.connecting,
    databases: state.newConnection.databases,
    connectionError: state.newConnection.connectionError,
    locale: state.global.locale,
    strings: state.global.strings,
    connected: state.global.host
});

NewConnection.propTypes = {
    classes: PropTypes.object.isRequired,
};

export default connect(mapStateToProps, {
    actionNewConnectionChangeProp, actionConnectToServer, actionOpenDatabase
})(withStyles(styles)(withCookies(NewConnection)));