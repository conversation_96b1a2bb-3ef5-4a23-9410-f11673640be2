// Auth action types
export const ACTION_AUTH_LOGIN_START = 'ACTION_AUTH_LOGIN_START';
export const ACTION_AUTH_LOGIN_SUCCESS = 'ACTION_AUTH_LOGIN_SUCCESS';
export const ACTION_AUTH_LOGIN_ERROR = 'ACTION_AUTH_LOGIN_ERROR';
export const ACTION_AUTH_LOGOUT = 'ACTION_AUTH_LOGOUT';
export const ACTION_AUTH_CLEAR_ERROR = 'ACTION_AUTH_CLEAR_ERROR';

// Auth utility functions
export const createAuthHeader = (username, password) => {
    const credentials = username + ':' + password;
    const encoded = btoa(credentials);
    return 'Basic ' + encoded;
};

export const getStoredCredentials = () => {
    try {
        const stored = localStorage.getItem('jviewer_auth');
        return stored ? JSON.parse(stored) : null;
    } catch (e) {
        console.error('Error reading stored credentials:', e);
        return null;
    }
};

export const storeCredentials = (host, port, username, password) => {
    try {
        const credentials = { host, port, username, password };
        localStorage.setItem('jviewer_auth', JSON.stringify(credentials));
    } catch (e) {
        console.error('Error storing credentials:', e);
    }
};

export const clearStoredCredentials = () => {
    try {
        localStorage.removeItem('jviewer_auth');
    } catch (e) {
        console.error('Error clearing credentials:', e);
    }
};

// Get databases list (no auth required)
const getDatabasesList = async (host, port) => {
    const url = `http://${host}:${port}/jv2/auth/`;
    console.log(`getDatabasesList: ${url}`);

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            },
            cache: 'no-cache',
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'auth.getDatabases',
                params: [],
                id: 1
            })
        });

        if (!response.ok) {
            throw new Error(`Server connection failed: ${response.status} ${response.statusText}`);
        }

        return response.json();
    } catch (error) {
        if (error.name === 'TypeError' && (error.message.includes('fetch') || error.message.includes('Failed to fetch'))) {
            throw new Error('Cannot connect to server. Please check if the server is running.');
        }
        throw error;
    }
};

// Test authentication using auth API
const testAuthentication = async (host, port, username, password) => {
    const authHeader = createAuthHeader(username, password);
    const url = `http://${host}:${port}/jv2/auth/`;
    console.log(`testAuthentication: ${url}`);

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': authHeader,
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            },
            cache: 'no-cache',
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'auth.validateCredentials',
                params: [],
                id: 2
            })
        });

        if (response.status === 401) {
            throw new Error('Invalid username or password');
        }

        if (!response.ok) {
            throw new Error(`Authentication failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        if (result.error) {
            throw new Error(result.error.message || 'Authentication failed');
        }

        if (!result.result || !result.result.valid) {
            throw new Error((result.result && result.result.message) || 'Invalid username or password');
        }

        return result;
    } catch (error) {
        // Only show "Cannot connect" for actual network errors, not auth errors
        if (error.name === 'TypeError' && (error.message.includes('fetch') || error.message.includes('Failed to fetch'))) {
            throw new Error('Cannot connect to server. Please check if the server is running.');
        }
        // For all other errors (including auth errors), pass them through
        throw error;
    }
};

// Action creators
export const actionLogin = (username, password) => async (dispatch) => {
    dispatch({ type: ACTION_AUTH_LOGIN_START });

    try {
        // Auto-determine host and port from current location
        const host = window.location.hostname;
        const port = window.location.port || (window.location.protocol === 'https:' ? '443' : '80');

        // First get databases list (no auth required)
        const databases = await getDatabasesList(host, port);
        console.log('Available databases:', databases);

        // Then test authentication with a method that requires auth
        await testAuthentication(host, port, username, password);

        // Store credentials including host and port
        storeCredentials(host, port, username, password);

        dispatch({
            type: ACTION_AUTH_LOGIN_SUCCESS,
            payload: {
                host,
                port,
                username,
                password,
                databases: databases.result || []
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        dispatch({
            type: ACTION_AUTH_LOGIN_ERROR,
            payload: error.message || 'Login failed'
        });
    }
};

export const actionLogout = () => (dispatch) => {
    clearStoredCredentials();
    dispatch({ type: ACTION_AUTH_LOGOUT });
};

export const actionClearAuthError = () => ({
    type: ACTION_AUTH_CLEAR_ERROR
});

// Load databases on app start
export const actionLoadDatabases = () => async (dispatch) => {
    dispatch({ type: ACTION_AUTH_LOGIN_START });

    try {
        // Auto-determine host and port from current location
        const host = window.location.hostname;
        const port = window.location.port || (window.location.protocol === 'https:' ? '443' : '80');

        // Get databases list (no auth required)
        const databases = await getDatabasesList(host, port);
        console.log('Available databases:', databases);

        dispatch({
            type: 'DATABASES_LOADED',
            payload: {
                host,
                port,
                databases: databases.result || []
            }
        });

    } catch (error) {
        console.error('Failed to load databases:', error);
        dispatch({
            type: ACTION_AUTH_LOGIN_ERROR,
            payload: error.message || 'Failed to connect to server'
        });
    }
};

// Auto-login with stored credentials
export const actionAutoLogin = () => async (dispatch) => {
    const credentials = getStoredCredentials();
    if (!credentials) {
        // If no stored credentials, just load databases
        dispatch(actionLoadDatabases());
        return;
    }

    const { host, port, username, password } = credentials;
    if (!host || !port || !username || !password) {
        clearStoredCredentials();
        dispatch(actionLoadDatabases());
        return;
    }

    try {
        // First get databases list
        const databases = await getDatabasesList(host, port);

        // Then test stored credentials
        await testAuthentication(host, port, username, password);

        dispatch({
            type: ACTION_AUTH_LOGIN_SUCCESS,
            payload: {
                host,
                port,
                username,
                password,
                databases: databases.result || []
            }
        });

    } catch (error) {
        console.error('Auto-login failed:', error);
        clearStoredCredentials();
        // Still load databases even if auto-login failed
        dispatch(actionLoadDatabases());
    }
};

export const actionSelectDatabase = (database) => ({
    type: 'SELECT_DATABASE',
    payload: database
});
