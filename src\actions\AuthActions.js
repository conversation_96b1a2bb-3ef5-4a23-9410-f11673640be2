// Auth action types
export const ACTION_AUTH_LOGIN_START = 'ACTION_AUTH_LOGIN_START';
export const ACTION_AUTH_LOGIN_SUCCESS = 'ACTION_AUTH_LOGIN_SUCCESS';
export const ACTION_AUTH_LOGIN_ERROR = 'ACTION_AUTH_LOGIN_ERROR';
export const ACTION_AUTH_LOGOUT = 'ACTION_AUTH_LOGOUT';
export const ACTION_AUTH_CLEAR_ERROR = 'ACTION_AUTH_CLEAR_ERROR';

// Auth utility functions
export const createAuthHeader = (username, password) => {
    const credentials = username + ':' + password;
    const encoded = btoa(credentials);
    return 'Basic ' + encoded;
};

export const getStoredCredentials = () => {
    try {
        const stored = localStorage.getItem('jviewer_auth');
        return stored ? JSON.parse(stored) : null;
    } catch (e) {
        console.error('Error reading stored credentials:', e);
        return null;
    }
};

export const storeCredentials = (username, password) => {
    try {
        const credentials = { username, password };
        localStorage.setItem('jviewer_auth', JSON.stringify(credentials));
    } catch (e) {
        console.error('Error storing credentials:', e);
    }
};

export const clearStoredCredentials = () => {
    try {
        localStorage.removeItem('jviewer_auth');
    } catch (e) {
        console.error('Error clearing credentials:', e);
    }
};

// Test authentication with a simple request
const testAuthentication = async (username, password) => {
    const authHeader = createAuthHeader(username, password);

    // Always use the mock server for testing
    const url = `http://localhost:8081/jv2/UBL/jsapi/`;

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': authHeader,
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            },
            cache: 'no-cache',
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'getDatabases',
                params: [],
                id: 1
            })
        });

        if (response.status === 401) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || 'Invalid username or password');
        }

        if (!response.ok) {
            throw new Error(`Authentication failed: ${response.status} ${response.statusText}`);
        }

        return response.json();
    } catch (error) {
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            throw new Error('Cannot connect to server. Please check if the server is running.');
        }
        throw error;
    }
};

// Action creators
export const actionLogin = (username, password) => async (dispatch) => {
    dispatch({ type: ACTION_AUTH_LOGIN_START });

    try {
        // Test authentication
        await testAuthentication(username, password);

        // Store credentials
        storeCredentials(username, password);

        dispatch({
            type: ACTION_AUTH_LOGIN_SUCCESS,
            payload: { username, password }
        });

    } catch (error) {
        console.error('Login error:', error);
        dispatch({
            type: ACTION_AUTH_LOGIN_ERROR,
            payload: error.message || 'Login failed'
        });
    }
};

export const actionLogout = () => (dispatch) => {
    clearStoredCredentials();
    dispatch({ type: ACTION_AUTH_LOGOUT });
};

export const actionClearAuthError = () => ({
    type: ACTION_AUTH_CLEAR_ERROR
});

// Auto-login with stored credentials
export const actionAutoLogin = () => async (dispatch) => {
    const credentials = getStoredCredentials();
    if (!credentials) {
        return;
    }

    const { username, password } = credentials;
    if (!username || !password) {
        clearStoredCredentials();
        return;
    }

    try {
        // Test stored credentials
        await testAuthentication(username, password);

        dispatch({
            type: ACTION_AUTH_LOGIN_SUCCESS,
            payload: { username, password }
        });

    } catch (error) {
        console.error('Auto-login failed:', error);
        clearStoredCredentials();
        // Don't dispatch error for auto-login failure
    }
};
