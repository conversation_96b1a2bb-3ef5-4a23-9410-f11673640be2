// Auth action types
export const ACTION_AUTH_LOGIN_START = 'ACTION_AUTH_LOGIN_START';
export const ACTION_AUTH_LOGIN_SUCCESS = 'ACTION_AUTH_LOGIN_SUCCESS';
export const ACTION_AUTH_LOGIN_ERROR = 'ACTION_AUTH_LOGIN_ERROR';
export const ACTION_AUTH_LOGOUT = 'ACTION_AUTH_LOGOUT';
export const ACTION_AUTH_CLEAR_ERROR = 'ACTION_AUTH_CLEAR_ERROR';

// Auth utility functions
export const createAuthHeader = (username, password) => {
    const credentials = username + ':' + password;
    const encoded = btoa(credentials);
    return 'Basic ' + encoded;
};

export const getStoredCredentials = () => {
    try {
        const stored = localStorage.getItem('jviewer_auth');
        return stored ? JSON.parse(stored) : null;
    } catch (e) {
        console.error('Error reading stored credentials:', e);
        return null;
    }
};

export const storeCredentials = (username, password) => {
    try {
        const credentials = { username, password };
        localStorage.setItem('jviewer_auth', JSON.stringify(credentials));
    } catch (e) {
        console.error('Error storing credentials:', e);
    }
};

export const clearStoredCredentials = () => {
    try {
        localStorage.removeItem('jviewer_auth');
    } catch (e) {
        console.error('Error clearing credentials:', e);
    }
};

// Test authentication with a simple request
const testAuthentication = async (username, password, host, port) => {
    const authHeader = createAuthHeader(username, password);
    
    // Use the buildApiUrl function from MainActions
    const url = `${host}:${port}/jv2/UBL/jsapi`;
    
    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'Authorization': authHeader,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            jsonrpc: '2.0',
            method: 'getDatabases',
            params: [],
            id: 1
        })
    });
    
    if (response.status === 401) {
        throw new Error('Invalid username or password');
    }
    
    if (!response.ok) {
        throw new Error(`Authentication failed: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
};

// Action creators
export const actionLogin = (username, password) => async (dispatch, getState) => {
    dispatch({ type: ACTION_AUTH_LOGIN_START });
    
    try {
        const state = getState();
        const host = state.newConnection.host;
        const port = state.newConnection.port;
        
        // Test authentication
        await testAuthentication(username, password, host, port);
        
        // Store credentials
        storeCredentials(username, password);
        
        dispatch({
            type: ACTION_AUTH_LOGIN_SUCCESS,
            payload: { username, password }
        });
        
    } catch (error) {
        console.error('Login error:', error);
        dispatch({
            type: ACTION_AUTH_LOGIN_ERROR,
            payload: error.message || 'Login failed'
        });
    }
};

export const actionLogout = () => (dispatch) => {
    clearStoredCredentials();
    dispatch({ type: ACTION_AUTH_LOGOUT });
};

export const actionClearAuthError = () => ({
    type: ACTION_AUTH_CLEAR_ERROR
});

// Auto-login with stored credentials
export const actionAutoLogin = () => async (dispatch, getState) => {
    const credentials = getStoredCredentials();
    if (!credentials) {
        return;
    }
    
    const { username, password } = credentials;
    if (!username || !password) {
        clearStoredCredentials();
        return;
    }
    
    try {
        const state = getState();
        const host = state.newConnection.host;
        const port = state.newConnection.port;
        
        // Test stored credentials
        await testAuthentication(username, password, host, port);
        
        dispatch({
            type: ACTION_AUTH_LOGIN_SUCCESS,
            payload: { username, password }
        });
        
    } catch (error) {
        console.error('Auto-login failed:', error);
        clearStoredCredentials();
        // Don't dispatch error for auto-login failure
    }
};
