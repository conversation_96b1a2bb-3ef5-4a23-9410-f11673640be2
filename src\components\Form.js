import React, { Component } from 'react'; 
import { connect } from 'react-redux';
import NewConnection from './NewConnection';
import Journal from './Journal';

class Form extends Component {

    render() {
        if (this.props.active === 'new') {
            return ( 
                <NewConnection /> 
            );
        }

        return (
            <Journal />
        );
    }
}

const mapStateToProps = (state) => ({
    active: state.tabs.active,
});

export default connect(mapStateToProps, 
    {})(Form);