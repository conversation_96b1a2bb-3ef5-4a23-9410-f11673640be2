import {
    ACTION_AUTH_LOGIN_START,
    ACTION_AUTH_LOGIN_SUCCESS,
    ACTION_AUTH_LOGIN_ERROR,
    ACTION_AUTH_LOGOUT,
    ACTION_AUTH_CLEAR_ERROR
} from '../actions/AuthActions';

export const DEFAULT_AUTH_STATE = {
    isAuthenticated: false,
    isLoggingIn: false,
    host: null,
    port: null,
    username: null,
    password: null,
    databases: [],
    databasesLoaded: false,
    error: null
};

export default (state = DEFAULT_AUTH_STATE, action) => {
    switch (action.type) {
        case ACTION_AUTH_LOGIN_START:
            return {
                ...state,
                isLoggingIn: true,
                error: null
            };
            
        case ACTION_AUTH_LOGIN_SUCCESS:
            return {
                ...state,
                isAuthenticated: true,
                isLoggingIn: false,
                host: action.payload.host,
                port: action.payload.port,
                username: action.payload.username,
                password: action.payload.password,
                databases: action.payload.databases || [],
                error: null
            };
            
        case ACTION_AUTH_LOGIN_ERROR:
            return {
                ...state,
                isAuthenticated: false,
                isLoggingIn: false,
                host: null,
                port: null,
                username: null,
                password: null,
                databases: [],
                error: action.payload
            };
            
        case ACTION_AUTH_LOGOUT:
            return {
                ...DEFAULT_AUTH_STATE
            };
            
        case ACTION_AUTH_CLEAR_ERROR:
            return {
                ...state,
                error: null
            };
            
        default:
            return state;
    }
};
