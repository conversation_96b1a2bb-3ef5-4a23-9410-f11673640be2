import React, { Component } from 'react';
import { connect } from 'react-redux';
import Tabs from './components/Tabs';
import IconButton from '@material-ui/core/IconButton';
import SvgIcon from '@material-ui/core/SvgIcon';
import Icon from '@material-ui/core/Icon';
import Form from './components/Form';
import Period from './components/Period';
import Filters from './components/Filters';
import LangSelector from './components/LangSelector';
import LoginForm from './components/LoginForm';
import { printRecords, downloadCsv, downloadText } from './utils/io';
import { doRequest } from './actions/MainActions';
import { actionAutoLogin } from './actions/AuthActions';
import { EXPORT_TXT_ICON, EXPORT_CSV_ICON } from './const/icons';

import 'antd/dist/antd.css';
import './App.css';
class App extends Component {
  constructor(props) {
    super(props);

    this.state = {
      anchorEl: null,
      print: false
    };

    this.timer = null;

    this.handlePrintClick = this.handlePrintClick.bind(this)
    this.handleExportTxtClick = this.handleExportTxtClick.bind(this)
    this.handleExportCsvClick = this.handleExportCsvClick.bind(this)
    this.handleRefreshClick = this.handleRefreshClick.bind(this)
  }

  componentDidMount() {
    // Try to auto-login with stored credentials
    this.props.actionAutoLogin();
  }

  handlePrintClick() {
    printRecords(this.props.records, this.props.strings.entryKinds);
  }

  handleExportTxtClick() {
    downloadText(this.props.records, this.props.strings);
  }

  handleExportCsvClick() {
    downloadCsv(this.props.records, this.props.strings);
  }

  handleRefreshClick() {
    if (this.props.active !== 'new') {
      this.props.doRequest(this.props.active, 0);
    }
  }

  renderPeriod() {
    if (this.props.active !== 'new') {
      return (
        <div id="period"><Period /></div>
      );
    }

    return null;
  }

  renderFilters() {
    if (this.props.active !== 'new') {
      return (
        <div id="filters"><Filters /></div>
      );
    }

    return null;
  }

  handleClosePrint = () => {
    this.setState({ print: false });
  }

  renderToolBar() {
    return (
      <div id="toolbar">
        <IconButton onClick={this.handlePrintClick} >
          <Icon style={{ fontSize: '2rem' }} className="toolbarIcon">
            print
          </Icon>
        </IconButton>

        <IconButton onClick={this.handleExportTxtClick} >
          <SvgIcon className="toolbarIcon" viewBox="0 0 548.291 548.291">
            {EXPORT_TXT_ICON}
          </SvgIcon>
        </IconButton>

        <IconButton onClick={this.handleExportCsvClick} >
          <SvgIcon className="toolbarIcon" viewBox="0 0 548.29 548.291">
            {EXPORT_CSV_ICON}
          </SvgIcon>
        </IconButton>

        <IconButton onClick={this.handleRefreshClick} >
          <Icon style={{ fontSize: '2rem' }} className="toolbarIcon">
            refresh
          </Icon>
        </IconButton>
      </div>
    );
  }

  renderSidebar() {
    if (this.props.active === 'new') {
      return null
    }

    return (
      <div id="sidebar">
        {this.renderToolBar()}
        {this.renderPeriod()}
        {this.renderFilters()}
      </div>
    );
  }

  render() {
    // Show login form if not authenticated
    if (!this.props.isAuthenticated) {
      return <LoginForm />;
    }

    return (
        <div id="root">
          <div id="top">
            <div id="logo">Journal</div>

            <Tabs />

            <div id="lang">
              <div style={{ float: 'left' }}>
                <LangSelector />
              </div>

            </div>
          </div>

          <div id="work">
            {this.renderSidebar()}

            <div id="form">
              <Form />
            </div>
          </div>
          <div id="footer"></div>
        </div>
    );
  }
}

const mapStateToProps = (state) => {
  const tab = state.tabs.items.find((tt) => tt.sessionId === state.tabs.active);
  const records = tab.data != null ? tab.data.records : null;
  return {
    active: state.tabs.active,
    locale: state.global.locale,
    strings: state.global.strings,
    records,
    isAuthenticated: state.auth.isAuthenticated
  }
}

export default connect(mapStateToProps, { doRequest, actionAutoLogin })(App);

