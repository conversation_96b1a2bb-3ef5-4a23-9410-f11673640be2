<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 (9.0.106) - Documentation Index</title><meta name="author" content="<PERSON>"><meta name="author" content="Remy Maucherat"><meta name="author" content="Yoav Shapira"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9</h1><div class="versionInfo">
            Version 9.0.106,
            <time datetime="2025-06-05">Jun 5 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 4.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 2.3 Javadocs</a></li><li><a href="elapi/index.html">EL 3.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 1.1 Javadocs</a></li><li><a href="jaspicapi/index.html">JASPIC 1.1 Javadocs</a></li><li><a href="annotationapi/index.html">Common Annotations 1.3 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Documentation Index</h2><h3 id="Introduction">Introduction</h3><div class="text">

<p>This is the top-level entry point of the documentation bundle for the
<strong>Apache Tomcat</strong> Servlet/JSP container.  Apache Tomcat version
9.0 implements the Servlet 4.0 and JavaServer Pages 2.3
<a href="https://cwiki.apache.org/confluence/display/TOMCAT/Specifications">specifications</a> from the
<a href="https://www.jcp.org">Java Community Process</a>, and includes many
additional features that make it a useful platform for developing and deploying
web applications and web services.</p>

<p>Select one of the links from the navigation menu (to the left) to drill
down to the more detailed documentation that is available.  Each available
manual is described in more detail below.</p>

</div><h3 id="Apache_Tomcat_User_Guide">Apache Tomcat User Guide</h3><div class="text">

<p>The following documents will assist you in downloading and installing
Apache Tomcat, and using many of the Apache Tomcat features.</p>

<ol>
<li><a href="introduction.html"><strong>Introduction</strong></a> - A
    brief, high level, overview of Apache Tomcat.</li>
<li><a href="setup.html"><strong>Setup</strong></a> - How to install and run
    Apache Tomcat on a variety of platforms.</li>
<li><a href="appdev/index.html"><strong>First web application</strong></a>
    - An introduction to the concepts of a <em>web application</em> as defined
    in the Servlet Specification.  Covers basic organization of your web application
    source tree, the structure of a web application archive, and an
    introduction to the web application deployment descriptor
    (<code>/WEB-INF/web.xml</code>).</li>
<li><a href="deployer-howto.html"><strong>Deployer</strong></a> -
    Operating the Apache Tomcat Deployer to deploy, precompile, and validate web
    applications.</li>
<li><a href="manager-howto.html"><strong>Manager</strong></a> -
    Operating the <strong>Manager</strong> web app to deploy, undeploy, and
    redeploy applications while Apache Tomcat is running.</li>
<li><a href="host-manager-howto.html"><strong>Host Manager</strong></a> -
    Operating the <strong>Host Manager</strong> web app to add and remove
    virtual hosts while Apache Tomcat is running.</li>
<li><a href="realm-howto.html"><strong>Realms and Access Control</strong></a>
    - Description of how to configure <em>Realms</em> (databases of users,
    passwords, and their associated roles) for use in web applications that
    utilize <em>Container Managed Security</em>.</li>
<li><a href="security-manager-howto.html"><strong>Security Manager</strong></a>
    - Configuring and using a Java Security Manager to
    support fine-grained control over the behavior of your web applications.
    </li>
<li><a href="jndi-resources-howto.html"><strong>JNDI Resources</strong></a>
    - Configuring standard and custom resources in the JNDI naming context
    that is provided to each web application.</li>
<li><a href="jndi-datasource-examples-howto.html">
    <strong>JDBC DataSource</strong></a>
    - Configuring a JNDI DataSource with a DB connection pool.
    Examples for many popular databases.</li>
<li><a href="class-loader-howto.html"><strong>Classloading</strong></a>
    - Information about class loading in Apache Tomcat, including where to place
    your application classes so that they are visible.</li>
<li><a href="jasper-howto.html"><strong>JSPs</strong></a>
    - Information about Jasper configuration, as well as the JSP compiler
    usage.</li>
<li><a href="ssl-howto.html"><strong>SSL/TLS</strong></a> -
    Installing and configuring SSL/TLS support so that your Apache Tomcat will
    serve requests using the <code>https</code> protocol.</li>
<li><a href="ssi-howto.html"><strong>SSI</strong></a> -
    Using Server Side Includes in Apache Tomcat.</li>
<li><a href="cgi-howto.html"><strong>CGI</strong></a> -
    Using CGIs with Apache Tomcat.</li>
<li><a href="proxy-howto.html"><strong>Proxy Support</strong></a> -
    Configuring Apache Tomcat to run behind a proxy server (or a web server
    functioning as a proxy server).</li>
<li><a href="mbeans-descriptors-howto.html"><strong>MBeans Descriptors</strong></a> -
    Configuring MBean descriptors files for custom components.</li>
<li><a href="default-servlet.html"><strong>Default Servlet</strong></a> -
    Configuring the default servlet and customizing directory listings.</li>
<li><a href="cluster-howto.html"><strong>Apache Tomcat Clustering</strong></a> -
    Enable session replication in a Apache Tomcat environment.</li>
<li><a href="balancer-howto.html"><strong>Balancer</strong></a> -
    Configuring, using, and extending the load balancer application.</li>
<li><a href="connectors.html"><strong>Connectors</strong></a> -
    Connectors available in Apache Tomcat, and native web server integration.</li>
<li><a href="monitoring.html"><strong>Monitoring and Management</strong></a> -
    Enabling JMX Remote support, and using tools to monitor and manage Apache Tomcat.</li>
<li><a href="logging.html"><strong>Logging</strong></a> -
    Configuring logging in Apache Tomcat.</li>
<li><a href="apr.html"><strong>Apache Portable Runtime</strong></a> -
    Using APR to provide superior performance, scalability and better
    integration with native server technologies.</li>
<li><a href="virtual-hosting-howto.html"><strong>Virtual Hosting</strong></a> -
    Configuring virtual hosting in Apache Tomcat.</li>
<li><a href="aio.html"><strong>Advanced IO</strong></a> -
    Extensions available over regular, blocking IO.</li>
<li><a href="maven-jars.html"><strong>Using Tomcat libraries with Maven</strong></a> -
    Obtaining Tomcat jars through Maven.</li>
<li><a href="security-howto.html"><strong>Security Considerations</strong></a> -
    Options to consider when securing an Apache Tomcat installation.</li>
<li><a href="windows-service-howto.html"><strong>Windows Service</strong></a> -
    Running Tomcat as a service on Microsoft Windows.</li>
<li><a href="windows-auth-howto.html"><strong>Windows Authentication</strong></a> -
    Configuring Tomcat to use integrated Windows authentication.</li>
<li><a href="jdbc-pool.html"><strong>High Concurrency JDBC Pool</strong></a> -
    Configuring Tomcat to use an alternative JDBC pool.</li>
<li><a href="web-socket-howto.html"><strong>WebSocket support</strong></a> -
    Developing WebSocket applications for Apache Tomcat.</li>
<li><a href="rewrite.html"><strong>URL rewrite</strong></a> -
    Using the regexp based rewrite valve for conditional URL and host rewrite.</li>
<li><a href="cdi.html"><strong>CDI and JAX-RS support</strong></a> -
    Configuring CDI,JAX-RS and Eclipse Microprofile support.</li>
<li><a href="graal.html"><strong>AOT compilation support</strong></a> -
    Ahead of Time compilation support with GraalVM/Native Image.</li>
</ol>

</div><h3 id="Reference">Reference</h3><div class="text">

<p>The following documents are aimed at <em>System Administrators</em> who
are responsible for installing, configuring, and operating an Apache Tomcat server.
</p>
<ul>
<li><a href="RELEASE-NOTES.txt"><strong>Release notes</strong></a>
    - Known issues in this Apache Tomcat release.
    </li>
<li><a href="config/index.html"><strong>Apache Tomcat Server Configuration Reference</strong></a>
    - Reference manual that documents all available elements and attributes
      that may be placed into the Apache Tomcat <code>conf/server.xml</code> file.
    </li>
<li><a href="https://tomcat.apache.org/connectors-doc/index.html"><strong>JK Documentation</strong></a>
    - Complete documentation and HOWTOs on the JK native webserver connector,
      used to interface Apache Tomcat with servers like Apache HTTPd, IIS
      and others.</li>
<li>Servlet 4.0
    <a href="https://jcp.org/aboutJava/communityprocess/final/jsr369/index.html">
    <strong>Specification</strong></a> and
    <a href="https://javaee.github.io/javaee-spec/javadocs/javax/servlet/package-summary.html">
    <strong>Javadoc</strong></a>
    </li>
<li>JSP 2.3
    <a href="https://jcp.org/aboutJava/communityprocess/mrel/jsr245/index2.html">
    <strong>Specification</strong></a> and
    <a href="http://docs.oracle.com/javaee/7/api/javax/servlet/jsp/package-summary.html">
    <strong>Javadoc</strong></a>
    </li>
<li>EL 3.0
    <a href="https://jcp.org/aboutJava/communityprocess/final/jsr341/index.html">
    <strong>Specification</strong></a> and
    <a href="http://docs.oracle.com/javaee/7/api/javax/el/package-summary.html">
    <strong>Javadoc</strong></a>
    </li>
<li>WebSocket 1.1
    <a href="https://jcp.org/aboutJava/communityprocess/mrel/jsr356/index.html">
    <strong>Specification</strong></a> and
    <a href="http://docs.oracle.com/javaee/7/api/javax/websocket/package-summary.html">
    <strong>Javadoc</strong></a>
    </li>
<li>JASPIC 1.1
    <a href="https://jcp.org/aboutJava/communityprocess/mrel/jsr196/index.html">
    <strong>Specification</strong></a> and
    <a href="http://docs.oracle.com/javaee/7/api/javax/security/auth/message/package-summary.html">
    <strong>Javadoc</strong></a>
    </li>
</ul>

</div><h3 id="Apache_Tomcat_Developers">Apache Tomcat Developers</h3><div class="text">

<p>The following documents are for Java developers who wish to contribute to
the development of the <em>Apache Tomcat</em> project.</p>
<ul>
<li><a href="building.html"><strong>Building from Source</strong></a> -
    Details the steps necessary to download Apache Tomcat source code (and the
    other packages that it depends on), and build a binary distribution from
    those sources.
    </li>
<li><a href="changelog.html"><strong>Changelog</strong></a> - Details the
    changes made to Apache Tomcat.
    </li>
<li><a href="https://wiki.apache.org/tomcat/TomcatVersions"><strong>Status</strong></a> -
    Apache Tomcat development status.
    </li>
<li><a href="developers.html"><strong>Developers</strong></a> - List of active
    Apache Tomcat contributors.
    </li>
<li><a href="api/index.html"><strong>Javadocs</strong></a>
    - Javadoc API documentation for Apache Tomcat's internals.</li>
<li><a href="architecture/index.html"><strong>Apache Tomcat Architecture</strong></a>
    - Documentation of the Apache Tomcat Server Architecture.</li>
</ul>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>