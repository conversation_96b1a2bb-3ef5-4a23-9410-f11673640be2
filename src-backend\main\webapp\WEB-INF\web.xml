<web-app xmlns="http://java.sun.com/xml/ns/javaee" version="3.0">
    <servlet>
        <servlet-name>ProxyServlet</servlet-name>
        <servlet-class>com.untill.ProxyServlet</servlet-class>
        <init-param>
            <param-name>targetHost</param-name>
            <param-value>http://*************</param-value>
        </init-param>
        <init-param>
            <param-name>targetPort</param-name>
            <param-value>6116</param-value>
        </init-param>
    </servlet>
    <servlet-mapping>
        <servlet-name>ProxyServlet</servlet-name>
        <url-pattern>/proxy/*</url-pattern>
    </servlet-mapping>
</web-app>
