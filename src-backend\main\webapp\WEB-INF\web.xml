<web-app xmlns="http://java.sun.com/xml/ns/javaee" version="3.0">
    <!-- Auth API Servlet for authentication and database listing -->
    <servlet>
        <servlet-name>AuthApiServlet</servlet-name>
        <servlet-class>com.untill.AuthApiServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>AuthApiServlet</servlet-name>
        <url-pattern>/auth/*</url-pattern>
    </servlet-mapping>

    <!-- Proxy Servlet for UBL API -->
    <servlet>
        <servlet-name>ProxyServlet</servlet-name>
        <servlet-class>com.untill.ProxyServlet</servlet-class>
        <load-on-startup>2</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>ProxyServlet</servlet-name>
        <url-pattern>/UBL/*</url-pattern>
    </servlet-mapping>
</web-app>
