package com.untill;

import com.untill.auth.BasicAuthUtils;
import com.untill.auth.UserRepository;
import com.untill.config.UntillConfigReader;

import javax.servlet.*;
import javax.servlet.http.*;
import java.io.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * Servlet for handling authentication and database listing API calls
 * Handles custom RPC methods without proxying to UBL
 */
public class AuthApiServlet extends HttpServlet {
	private static final Logger LOGGER = Logger.getLogger(AuthApiServlet.class.getName());
	private static final long serialVersionUID = 1L;
	
	private UserRepository userRepository;
	private ObjectMapper objectMapper;

	@Override
	public void init() throws ServletException {
		super.init();
		
		// Initialize user repository
		userRepository = new UserRepository();
		objectMapper = new ObjectMapper();
		
		LOGGER.info("AuthApiServlet initialized");
	}

	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response) 
			throws ServletException, IOException {
		
		// Add CORS headers
		addCorsHeaders(response);
		
		// Read JSON-RPC request
		String requestBody = getRequestBody(request);
		if (requestBody == null || requestBody.trim().isEmpty()) {
			sendErrorResponse(response, -32700, "Parse error", null);
			return;
		}
		
		try {
			JsonNode jsonRequest = objectMapper.readTree(requestBody);
			String method = jsonRequest.get("method").asText();
			JsonNode params = jsonRequest.get("params");
			JsonNode id = jsonRequest.get("id");
			
			LOGGER.info("Received auth API request: " + method);
			
			Object result = null;
			switch (method) {
				case "auth.getDatabases":
					result = handleGetDatabases();
					break;
				case "auth.validateCredentials":
					result = handleValidateCredentials(request);
					break;
				default:
					sendErrorResponse(response, -32601, "Method not found", id);
					return;
			}
			
			sendSuccessResponse(response, result, id);
			
		} catch (Exception e) {
			LOGGER.log(Level.SEVERE, "Error processing auth API request", e);
			sendErrorResponse(response, -32603, "Internal error", null);
		}
	}

	@Override
	protected void doOptions(HttpServletRequest request, HttpServletResponse response) 
			throws ServletException, IOException {
		addCorsHeaders(response);
		response.setStatus(HttpServletResponse.SC_OK);
	}

	/**
	 * Handle getDatabases request - returns list of available databases
	 */
	private List<Map<String, String>> handleGetDatabases() {
		List<Map<String, String>> databases = new ArrayList<>();
		
		try {
			String untillHome = UntillConfigReader.getUntillHome();
			File dbDir = new File(untillHome, "DB");
			
			if (dbDir.exists() && dbDir.isDirectory()) {
				File[] fdbFiles = dbDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".fdb"));
				
				if (fdbFiles != null) {
					for (File fdbFile : fdbFiles) {
						Map<String, String> db = new HashMap<>();
						String dbName = fdbFile.getName().replaceAll("\\.fdb$", "");
						db.put("name", dbName);
						db.put("description", "Database: " + dbName);
						db.put("path", fdbFile.getAbsolutePath());
						databases.add(db);
					}
				}
			}
			
			// Add some default databases if none found
			if (databases.isEmpty()) {
				Map<String, String> testDb1 = new HashMap<>();
				testDb1.put("name", "TestDB1");
				testDb1.put("description", "Test Database 1");
				databases.add(testDb1);
				
				Map<String, String> testDb2 = new HashMap<>();
				testDb2.put("name", "TestDB2");
				testDb2.put("description", "Test Database 2");
				databases.add(testDb2);
			}
			
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Error getting databases list", e);
		}
		
		LOGGER.info("Returning " + databases.size() + " databases");
		return databases;
	}

	/**
	 * Handle validateCredentials request - validates user credentials
	 */
	private Map<String, Object> handleValidateCredentials(HttpServletRequest request) {
		Map<String, Object> result = new HashMap<>();
		
		BasicAuthUtils.Credentials credentials = BasicAuthUtils.extractCredentials(request);
		if (credentials == null) {
			result.put("valid", false);
			result.put("message", "No credentials provided");
			return result;
		}
		
		boolean isValid = userRepository.authenticate(credentials.getUsername(), credentials.getPassword());
		result.put("valid", isValid);
		result.put("username", credentials.getUsername());
		
		if (isValid) {
			result.put("message", "Credentials valid");
			LOGGER.info("Credentials validated for user: " + credentials.getUsername());
		} else {
			result.put("message", "Invalid credentials");
			LOGGER.warning("Invalid credentials for user: " + credentials.getUsername());
		}
		
		return result;
	}

	/**
	 * Read request body as string
	 */
	private String getRequestBody(HttpServletRequest request) {
		try {
			StringBuilder body = new StringBuilder();
			String line;
			BufferedReader reader = request.getReader();
			while ((line = reader.readLine()) != null) {
				body.append(line);
			}
			return body.toString();
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Error reading request body", e);
			return null;
		}
	}

	/**
	 * Send JSON-RPC success response
	 */
	private void sendSuccessResponse(HttpServletResponse response, Object result, JsonNode id) 
			throws IOException {
		Map<String, Object> jsonResponse = new HashMap<>();
		jsonResponse.put("jsonrpc", "2.0");
		jsonResponse.put("result", result);
		jsonResponse.put("id", id != null ? id.asText() : null);
		
		response.setContentType("application/json");
		response.setCharacterEncoding("UTF-8");
		response.getWriter().write(objectMapper.writeValueAsString(jsonResponse));
	}

	/**
	 * Send JSON-RPC error response
	 */
	private void sendErrorResponse(HttpServletResponse response, int code, String message, JsonNode id) 
			throws IOException {
		Map<String, Object> error = new HashMap<>();
		error.put("code", code);
		error.put("message", message);
		
		Map<String, Object> jsonResponse = new HashMap<>();
		jsonResponse.put("jsonrpc", "2.0");
		jsonResponse.put("error", error);
		jsonResponse.put("id", id != null ? id.asText() : null);
		
		response.setStatus(HttpServletResponse.SC_OK); // JSON-RPC errors use 200 OK
		response.setContentType("application/json");
		response.setCharacterEncoding("UTF-8");
		response.getWriter().write(objectMapper.writeValueAsString(jsonResponse));
	}

	/**
	 * Add CORS headers for development
	 */
	private void addCorsHeaders(HttpServletResponse response) {
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, Accept");
		response.setHeader("Access-Control-Max-Age", "3600");
	}

	@Override
	public void destroy() {
		super.destroy();
		if (userRepository != null) {
			userRepository.shutdown();
		}
	}
}
