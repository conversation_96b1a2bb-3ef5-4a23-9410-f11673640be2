package com.untill;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Base64;
import java.util.logging.Level;

/**
 * Extended ProxyServlet with basic authentication support.
 * This is a template for future implementation of password-based authorization.
 */
public class AuthenticatedProxyServlet extends ProxyServlet {
	private static final long serialVersionUID = 1L;

	private String requiredUsername;
	private String requiredPassword;
	private boolean authenticationEnabled;

	@Override
	public void init() throws ServletException {
		super.init();

		// Get authentication parameters from web.xml
		requiredUsername = getInitParameter("authUsername");
		requiredPassword = getInitParameter("authPassword");
		String authEnabledStr = getInitParameter("authEnabled");

		authenticationEnabled = "true".equalsIgnoreCase(authEnabledStr);

		if (authenticationEnabled) {
			if (requiredUsername == null || requiredPassword == null) {
				LOGGER.log(Level.WARNING, "Authentication is enabled but username or password is not configured");
				authenticationEnabled = false;
			} else {
				LOGGER.info("Authentication enabled for ProxyServlet");
			}
		} else {
			LOGGER.info("Authentication disabled for ProxyServlet");
		}
	}

	@Override
	protected void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		if (authenticationEnabled && !isAuthenticated(request)) {
			// Send 401 Unauthorized
			response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
			response.setHeader("WWW-Authenticate", "Basic realm=\"Proxy Access\"");
			response.getWriter().write("Authentication required");
			return;
		}

		// If authentication passed or disabled, proceed with normal proxy behavior
		super.service(request, response);
	}

	/**
	 * Check if the request contains valid authentication credentials
	 */
	private boolean isAuthenticated(HttpServletRequest request) {
		String authHeader = request.getHeader("Authorization");
		if (authHeader == null || !authHeader.startsWith("Basic ")) {
			return false;
		}

		try {
			String encodedCredentials = authHeader.substring("Basic ".length());
			String credentials = new String(Base64.getDecoder().decode(encodedCredentials));
			String[] parts = credentials.split(":", 2);

			if (parts.length != 2) {
				return false;
			}

			String username = parts[0];
			String password = parts[1];

			return requiredUsername.equals(username) && requiredPassword.equals(password);
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Error parsing authentication credentials", e);
			return false;
		}
	}
}
