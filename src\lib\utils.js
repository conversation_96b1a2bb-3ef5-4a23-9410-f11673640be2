import _ from 'lodash';

export const validatedItems = {};

export function rc(strings, key, opts) {
    if (!strings.dictionary) {
        return key
    }

    let lexem = strings.dictionary[key] || key || "";

    if (!_.isNil(opts) && _.isArray(opts)) {
        opts.forEach((v, i) => {
            lexem = String(lexem).replace(`{${i}}`, v);
        });
    }

    return lexem;
}

export function isValidated(sessionId, id) {
    const session = validatedItems[sessionId];
    if (session) {
        if (session[id] === false) {
            return false;
        }
        return session[id] || null;
    }
    return null;
}

export function validated(sessionId, id, val) {
    let session = validatedItems[sessionId];
    if (!session) {
        session = {}
    }
    validatedItems[sessionId] = {
        ...session,
        [id]: val
    };
    const el = document.getElementById(`v${id}`);
    if (el) {
        el.className = val
            ? 'valid'
            : 'invalid';
    }
//    console.log('validated ', id, val, validatedItems);
}
