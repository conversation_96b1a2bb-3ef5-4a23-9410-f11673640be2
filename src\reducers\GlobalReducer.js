import { ACTION_GLOBAL_WINDOW_RESIZE, ACTION_GLOBAL_SET_STRINGS, ACTION_NEWCONN_CONNECT_SUCCESS } from "../actions/Types";

export const DEFAULT_GLOBAL_STATE = {
    locale: null,
    clientHeight: 400,
    strings: {}
};

export default (state = DEFAULT_GLOBAL_STATE, action) => {
    switch (action.type) { 
    case ACTION_GLOBAL_WINDOW_RESIZE: {
        return {
            ...state,
            clientHeight: window.innerHeight - action.payload.headerHeight
        }
    }
    case ACTION_NEWCONN_CONNECT_SUCCESS:
        return {
            ...state,
            host: action.payload.host,
            port: action.payload.port
        }
    case ACTION_GLOBAL_SET_STRINGS: {
        // console.log('ACTION_GLOBAL_SET_STRINGS', action.payload);
        return {
            ...state,
            strings: action.payload.strings,
            locale: action.payload.locale,
        }
    }
    default:
        return state;
    }
};
