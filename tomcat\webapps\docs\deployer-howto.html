<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 (9.0.106) - Tomcat Web Application Deployment</title><meta name="author" content="Allistair Crossley"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9</h1><div class="versionInfo">
            Version 9.0.106,
            <time datetime="2025-06-05">Jun 5 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 4.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 2.3 Javadocs</a></li><li><a href="elapi/index.html">EL 3.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 1.1 Javadocs</a></li><li><a href="jaspicapi/index.html">JASPIC 1.1 Javadocs</a></li><li><a href="annotationapi/index.html">Common Annotations 1.3 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Tomcat Web Application Deployment</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Installation">Installation</a></li><li><a href="#A_word_on_Contexts">A word on Contexts</a></li><li><a href="#Deployment_on_Tomcat_startup">Deployment on Tomcat startup</a></li><li><a href="#Deploying_on_a_running_Tomcat_server">Deploying on a running Tomcat server</a></li><li><a href="#Deploying_using_the_Tomcat_Manager">Deploying using the Tomcat Manager</a></li><li><a href="#Deploying_using_the_Client_Deployer_Package">Deploying using the Client Deployer Package</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">
        <p>
            Deployment is the term used for the process of installing a web
            application (either a 3rd party WAR or your own custom web application)
            into the Tomcat server.
        </p>
        <p>
            Web application deployment may be accomplished in a number of ways
            within the Tomcat server.
        </p>
        <ul>
                <li>Statically; the web application is setup before Tomcat is started</li>
                <li>
                    Dynamically; by directly manipulating already deployed web
                    applications (relying on <em>auto-deployment</em>
                    feature) or remotely by using the Tomcat Manager web
                    application
                </li>
        </ul>
        <p>
            The <a href="manager-howto.html">Tomcat Manager</a> is a web
            application that can be used interactively (via HTML GUI) or
            programmatically (via URL-based API) to deploy and manage web
            applications.
        </p>
        <p>
            There are a number of ways to perform deployment that rely on
            the Manager web application. Apache Tomcat provides tasks
            for Apache Ant build tool.
            <a href="https://tomcat.apache.org/maven-plugin.html">Apache Tomcat Maven Plugin</a>
            project provides integration with Apache Maven.
            There is also a tool called the Client Deployer, which can be
            used from a command line and provides additional functionality
            such as compiling and validating web applications as well as
            packaging web application into web application resource (WAR)
            files.
        </p>
    </div><h3 id="Installation">Installation</h3><div class="text">
        <p>
            There is no installation required for static deployment of web
            applications as this is provided out of the box by Tomcat. Nor is any
            installation required for deployment functions with the Tomcat Manager,
            although some configuration is required as detailed in the
            <a href="manager-howto.html">Tomcat Manager manual</a>.
            An installation is however required if you wish
            to use the Tomcat Client Deployer (TCD).
        </p>
        <p>
            The TCD is not packaged with the Tomcat core
            distribution, and must therefore be downloaded separately from
            the Downloads area. The download is usually labelled
            <i>apache-tomcat-9.0.x-deployer</i>.
        </p>
        <p>
            TCD has prerequisites of Apache Ant 1.6.2+ and a Java installation.
            Your environment should define an ANT_HOME environment value pointing to
            the root of your Ant installation, and a JAVA_HOME value pointing to
            your Java installation. Additionally, you should ensure Ant's ant
            command, and the Java javac compiler command run from the command shell
            that your operating system provides.
        </p>
        <ol>
            <li>Download the TCD distribution</li>
            <li>
                The TCD package need not be extracted into any existing Tomcat
                installation, it can be extracted to any location.
            </li>
            <li>Read Using the <a href="#Deploying_using_the_Client_Deployer_Package">
            Tomcat Client Deployer</a></li>
        </ol>
    </div><h3 id="A_word_on_Contexts">A word on Contexts</h3><div class="text">
        <p>
            In talking about deployment of web applications, the concept of a
            <i>Context</i> is required to be understood. A Context is what Tomcat
            calls a web application.
        </p>
        <p>
            In order to configure a Context within Tomcat a <i>Context Descriptor</i>
            is required. A Context Descriptor is simply an XML file that contains
            Tomcat related configuration for a Context, e.g naming resources or
            session manager configuration. In earlier versions of
            Tomcat the content of a Context Descriptor configuration was often stored within
            Tomcat's primary configuration file <i>server.xml</i> but this is now
            discouraged (although it currently still works).
        </p>
        <p>
            Context Descriptors not only help Tomcat to know how to configure
            Contexts but other tools such as the Tomcat Manager and TCD often use
            these Context Descriptors to perform their roles properly.
        </p>
        <p>
            The locations for Context Descriptors are:
        </p>
        <ol>
                <li>$CATALINA_BASE/conf/[enginename]/[hostname]/[webappname].xml</li>
                <li>$CATALINA_BASE/webapps/[webappname]/META-INF/context.xml</li>
        </ol>
        <p>
            Files in (1) are named [webappname].xml but files in (2) are named
            context.xml. If a Context Descriptor is not provided for a Context,
            Tomcat configures the Context using default values.
        </p>
    </div><h3 id="Deployment_on_Tomcat_startup">Deployment on Tomcat startup</h3><div class="text">
        <p>
            If you are not interested in using the Tomcat Manager, or TCD,
            then you'll need to deploy your web applications
            statically to Tomcat, followed by a Tomcat startup. The location you
            deploy web applications to for this type of deployment is called the
            <code>appBase</code> which is specified per Host. You either copy a
            so-called <i>exploded web application</i>, i.e non-compressed, to this
            location, or a compressed web application resource .WAR file.
        </p>
        <p>
            The web applications present in the location specified by the Host's
            (default Host is "localhost") <code>appBase</code> attribute (default
            appBase is "$CATALINA_BASE/webapps") will be deployed on Tomcat startup
            only if the Host's <code>deployOnStartup</code> attribute is "true".
        </p>
        <p>
            The following deployment sequence will occur on Tomcat startup in that
            case:
        </p>
        <ol>
            <li>Any Context Descriptors will be deployed first.</li>
            <li>
                Exploded web applications not referenced by any Context
                Descriptor will then be deployed. If they have an associated
                .WAR file in the appBase and it is newer than the exploded web application,
                the exploded directory will be removed and the webapp will be
                redeployed from the .WAR
            </li>
            <li>.WAR files will be deployed</li>
        </ol>
    </div><h3 id="Deploying_on_a_running_Tomcat_server">Deploying on a running Tomcat server</h3><div class="text">
        <p>
            It is possible to deploy web applications to a running Tomcat server.
        </p>
        <p>
            If the Host <code>autoDeploy</code> attribute is "true", the Host will
            attempt to deploy and update web applications dynamically, as needed,
            for example if a new .WAR is dropped into the <code>appBase</code>.
            For this to work, the Host needs to have background processing
            enabled which is the default configuration.
        </p>

        <p>
            <code>autoDeploy</code> set to "true" and a running Tomcat allows for:
        </p>
        <ul>
            <li>Deployment of .WAR files copied into the Host <code>appBase</code>.</li>
            <li>
                Deployment of exploded web applications which are
                copied into the Host <code>appBase</code>.
            </li>
            <li>
                Re-deployment of a web application which has already been deployed from
                a .WAR when the new .WAR is provided. In this case the exploded
                web application is removed, and the .WAR is expanded again.
                Note that the explosion will not occur if the Host is configured
                so that .WARs are not exploded with a <code>unpackWARs</code>
                attribute set to "false", in which case the web application
                will be simply redeployed as a compressed archive.
            </li>
            <li>
                Re-loading of a web application if the /WEB-INF/web.xml file (or
                any other resource defined as a WatchedResource) is updated.
            </li>
            <li>
                Re-deployment of a web application if the Context Descriptor
                file from which the web application has been deployed is
                updated.
            </li>
            <li>
                Re-deployment of dependent web applications if the global or
                per-host Context Descriptor file used by the web application is
                updated.
            </li>
            <li>
                Re-deployment of a web application if a Context Descriptor file (with a
                filename corresponding to the Context path of the previously deployed
                web application) is added to the
                <code>$CATALINA_BASE/conf/[enginename]/[hostname]/</code>
                directory.
            </li>
            <li>
                Undeployment of a web application if its document base (docBase)
                is deleted. Note that on Windows, this assumes that anti-locking
                features (see Context configuration) are enabled, otherwise it is not
                possible to delete the resources of a running web application.
            </li>
        </ul>
        <p>
            Note that web application reloading can also be configured in the loader, in which
            case loaded classes will be tracked for changes.
        </p>
    </div><h3 id="Deploying_using_the_Tomcat_Manager">Deploying using the Tomcat Manager</h3><div class="text">
        <p>
            The Tomcat Manager is covered in its <a href="manager-howto.html">own manual page</a>.
        </p>
    </div><h3 id="Deploying_using_the_Client_Deployer_Package">Deploying using the Client Deployer Package</h3><div class="text">
        <p>
            Finally, deployment of web application may be achieved using the
            Tomcat Client Deployer. This is a package which can be used to
            validate, compile, compress to .WAR, and deploy web applications to
            production or development Tomcat servers. It should be noted that this feature
            uses the Tomcat Manager and as such the target Tomcat server should be
            running.
        </p>

        <p>
            It is assumed the user will be familiar with Apache Ant for using the TCD.
            Apache Ant is a scripted build tool. The TCD comes pre-packaged with a
            build script to use. Only a modest understanding of Apache Ant is
            required (installation as listed earlier in this page, and familiarity
            with using the operating system command shell and configuring
            environment variables).
        </p>

        <p>
            The TCD includes Ant tasks, the Jasper page compiler for JSP compilation
            before deployment, as well as a task which
            validates the web application Context Descriptor. The validator task (class
            <code>org.apache.catalina.ant.ValidatorTask</code>) allows only one parameter:
            the base path of an exploded web application.
        </p>

        <p>
            The TCD uses an exploded web application as input (see the list of the
            properties used below). A web application that is programmatically
            deployed with the deployer may include a Context Descriptor in
            <code>/META-INF/context.xml</code>.
        </p>

        <p>
            The TCD includes a ready-to-use Ant script, with the following targets:
        </p>
        <ul>
            <li>
                <code>compile</code> (default): Compile and validate the web
                application. This can be used standalone, and does not need a running
                Tomcat server. The compiled application will only run on the associated
                <em>Tomcat&nbsp;X.Y.Z</em> server release, and is not guaranteed to work
                on another Tomcat release, as the code generated by Jasper depends on its runtime
                component. It should also be noted that this target will also compile
                automatically any Java source file located in the
                <code>/WEB-INF/classes</code> folder of the web application.</li>
            <li>
                <code>deploy</code>: Deploy a web application (compiled or not) to
                a Tomcat server.
            </li>
            <li><code>undeploy</code>: Undeploy a web application</li>
            <li><code>start</code>: Start web application</li>
            <li><code>reload</code>: Reload web application</li>
            <li><code>stop</code>: Stop web application</li>
        </ul>

        <p>
            In order for the deployment to be configured, create a file
            called <code>deployer.properties</code> in the TCD installation
            directory root. In this file, add the following name=value pairs per
            line:
        </p>

        <p>
            Additionally, you will need to ensure that a user has been
            setup for the target Tomcat Manager (which TCD uses) otherwise the TCD
            will not authenticate with the Tomcat Manager and the deployment will
            fail. To do this, see the Tomcat Manager page.
        </p>

        <ul>
            <li>
                <code>build</code>: The build folder used will be, by default,
                <code>${build}/webapp/${path}</code> (<code>${build}</code>, by
                default, points to <code>${basedir}/build</code>). After the end
                of the execution of the <code>compile</code> target, the web
                application .WAR will be located at
                <code>${build}/webapp/${path}.war</code>.
            </li>
            <li>
                <code>webapp</code>: The directory containing the exploded web application
                which will be compiled and validated. By default, the folder is
                <code>myapp</code>.
            </li>
            <li>
                <code>path</code>: Deployed context path of the web application,
                by default <code>/myapp</code>.
            </li>
            <li>
                <code>url</code>: Absolute URL to the Tomcat Manager web application of a
                running Tomcat server, which will be used to deploy and undeploy the
                web application. By default, the deployer will attempt to access
                a Tomcat instance running on localhost, at
                <code>http://localhost:8080/manager/text</code>.
            </li>
            <li>
                <code>username</code>: Tomcat Manager username (user should have a role of
                manager-script)
            </li>
            <li><code>password</code>: Tomcat Manager password.</li>
        </ul>
    </div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>