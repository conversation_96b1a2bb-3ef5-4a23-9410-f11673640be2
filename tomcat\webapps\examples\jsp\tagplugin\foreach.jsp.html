<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;html>
  &lt;head>
    &lt;title>Tag Plugin Examples: forEach&lt;/title>
  &lt;/head>
  &lt;body>
    &lt;h1>Tag Plugin Examples - &amp;lt;c:forEach>&lt;/h1>

    &lt;hr/>
    &lt;br/>
    &lt;a href="notes.html">Plugin Introductory Notes&lt;/a>
    &lt;br/>
    &lt;a href="howto.html">Brief Instructions for Writing Plugins&lt;/a>
    &lt;br/> &lt;br/>
    &lt;hr/>

    &lt;br/>

    &lt;%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
    &lt;%@ page import="java.util.Vector" %>

    &lt;h3>Iterating over a range&lt;/h3>
    &lt;c:forEach var="item" begin="1" end="10">
        ${item}
    &lt;/c:forEach>

    &lt;% Vector&lt;String> v = new Vector&lt;>();
        v.add("One"); v.add("Two"); v.add("Three"); v.add("Four");

        pageContext.setAttribute("vector", v);
    %>

    &lt;h3>Iterating over a Vector&lt;/h3>

    &lt;c:forEach items="${vector}" var="item" >
        ${item}
    &lt;/c:forEach>
  &lt;/body>
&lt;/html>
</pre></body></html>