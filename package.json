{"name": "jv2", "version": "0.1.0", "private": true, "homepage": "/jv2", "dependencies": {"@material-ui/core": "^1.4.1", "antd": "^4.15.2", "file-saver": "^1.3.8", "json2csv": "^4.2.1", "lodash": "^4.17.21", "moment": "^2.22.2", "prop-types": "^15.7.2", "react": "^16.4.1", "react-cookie": "^2.2.0", "react-css-transition-replace": "^3.0.3", "react-datepicker": "^1.5.0", "react-dom": "^16.4.1", "react-infinite-scroller": "^1.2.0", "react-redux": "^5.0.7", "react-scripts": "1.1.4", "redux": "^4.0.0", "redux-thunk": "^2.3.0", "stream-to-blob": "^1.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject"}, "devDependencies": {"eslint": "^5.1.0", "eslint-plugin-react": "^7.10.0"}}