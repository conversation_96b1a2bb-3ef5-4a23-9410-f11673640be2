<?xml version="1.0" encoding="ISO-8859-1" ?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE taglib
        PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN"
        "http://java.sun.com/dtd/web-jsptaglibrary_1_2.dtd">

<!-- a tag library descriptor -->

<taglib>
  <tlib-version>1.0</tlib-version>
  <jsp-version>1.2</jsp-version>
  <short-name>debug</short-name>
  <uri>http://tomcat.apache.org/debug-taglib</uri>
  <description>
    This tag library defines no tags.  Instead, its purpose is encapsulated
    in the TagLibraryValidator implementation that simply outputs the XML
    version of a JSP page to standard output, whenever this tag library is
    referenced in a "taglib" directive in a JSP page.
  </description>
  <validator>
    <validator-class>validators.DebugValidator</validator-class>
  </validator>

  <!-- This is a dummy tag solely to satisfy DTD requirements -->
  <tag>
    <name>log</name>
    <tag-class>examples.LogTag</tag-class>
    <body-content>TAGDEPENDENT</body-content>
    <description>
        Perform a server side action; Log the message.
    </description>
    <attribute>
        <name>toBrowser</name>
        <required>false</required>
    </attribute>
  </tag>


</taglib>
