<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 Configuration Reference (9.0.106) - The Cluster Receiver object</title><meta name="author" content="Filip Hanik"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9 Configuration Reference</h1><div class="versionInfo">
            Version 9.0.106,
            <time datetime="2025-06-05">Jun 5 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="runtime-attributes.html">Runtime attributes</a></li><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">JASPIC</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>The Cluster Receiver object</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Blocking_vs_Non-Blocking_Receiver">Blocking vs Non-Blocking Receiver</a></li><li><a href="#Attributes">Attributes</a><ol><li><a href="#Common_Attributes">Common Attributes</a></li><li><a href="#NioReceiver">NioReceiver</a></li><li><a href="#BioReceiver">BioReceiver</a></li></ol></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">
  <p>
  The receiver component is responsible for receiving cluster messages.
  As you might notice through the configuration, is that the receiving of messages
  and sending of messages are two different components, this is different from many other
  frameworks, but there is a good reason for it, to decouple the logic for how messages are sent from
  how messages are received.<br>
  The receiver is very much like the Tomcat Connector, its the base of the thread pool
  for incoming cluster messages. The receiver is straight forward, but all the socket settings
  for incoming traffic are managed here.
  </p>
</div><h3 id="Blocking_vs_Non-Blocking_Receiver">Blocking vs Non-Blocking Receiver</h3><div class="text">
  <p>
  The receiver supports both a non blocking, <code>org.apache.catalina.tribes.transport.nio.NioReceiver</code>, and a
  blocking, <code>org.apache.catalina.tribes.transport.bio.BioReceiver</code>. It is preferred to use the non blocking receiver
  to be able to grow your cluster without running into thread starvation.<br>
  Using the non blocking receiver allows you to with a very limited thread count to serve a large number of messages.
  Usually the rule is to use 1 thread per node in the cluster for small clusters, and then depending on your message frequency
  and your hardware, you'll find an optimal number of threads peak out at a certain number.
  </p>
</div><h3 id="Attributes">Attributes</h3><div class="text">
  <div class="subsection"><h4 id="Common_Attributes">Common Attributes</h4><div class="text">
  <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Common Attributes_className"><td><strong><code class="attributeName">className</code></strong></td><td>
      The implementation of the receiver component. Two implementations available,
      <code>org.apache.catalina.tribes.transport.nio.NioReceiver</code> and
      <code>org.apache.catalina.tribes.transport.bio.BioReceiver</code>.<br>
      The <code>org.apache.catalina.tribes.transport.nio.NioReceiver</code> is the
      preferred implementation
    </td></tr><tr id="Attributes_Common Attributes_address"><td><code class="attributeName">address</code></td><td>
      The address (network interface) to listen for incoming traffic.
      Same as the bind address. The default value is <code>auto</code> and translates to
      <code>java.net.InetAddress.getLocalHost().getHostAddress()</code>.
    </td></tr><tr id="Attributes_Common Attributes_direct"><td><code class="attributeName">direct</code></td><td>
      Possible values are <code>true</code> or <code>false</code>.
      Set to true if you want the receiver to use direct bytebuffers when reading data
      from the sockets.
    </td></tr><tr id="Attributes_Common Attributes_port"><td><code class="attributeName">port</code></td><td>
      The listen port for incoming data. The default value is <code>4000</code>.
      To avoid port conflicts the receiver will automatically bind to a free port within the range of
      <code> port &lt;= bindPort &lt; port+autoBind</code>
      So for example, if port is 4000, and autoBind is set to 10, then the receiver will open up
      a server socket on the first available port in the range 4000-4009.
    </td></tr><tr id="Attributes_Common Attributes_autoBind"><td><code class="attributeName">autoBind</code></td><td>
      Default value is <code>100</code>.
      Use this value if you wish to automatically avoid port conflicts the cluster receiver will try to open a
      server socket on the <code>port</code> attribute port, and then work up <code>autoBind</code> number of times.
    </td></tr><tr id="Attributes_Common Attributes_securePort"><td><code class="attributeName">securePort</code></td><td>
      The secure listen port. This port is SSL enabled. If this attribute is omitted no SSL port is opened up.
      There default value is unset, meaning there is no SSL socket available.
    </td></tr><tr id="Attributes_Common Attributes_udpPort"><td><code class="attributeName">udpPort</code></td><td>
      The UDP listen port. If this attribute is omitted no UDP port is opened up.
      There default value is unset, meaning there is no UDP listener available.
    </td></tr><tr id="Attributes_Common Attributes_selectorTimeout"><td><code class="attributeName">selectorTimeout</code></td><td>
      The value in milliseconds for the polling timeout in the <code>NioReceiver</code>. On older versions of the JDK
      there have been bugs, that should all now be cleared out where the selector never woke up.
      The default value is a very high <code>5000</code> milliseconds.
    </td></tr><tr id="Attributes_Common Attributes_maxThreads"><td><code class="attributeName">maxThreads</code></td><td>
      The maximum number of threads in the receiver thread pool. The default value is <code>15</code>
      Adjust this value relative to the number of nodes in the cluster, the number of messages being exchanged and
      the hardware you are running on. A higher value doesn't mean more efficiency, tune this value according to your
      own test results.
    </td></tr><tr id="Attributes_Common Attributes_minThreads"><td><code class="attributeName">minThreads</code></td><td>
      Minimum number of threads to be created when the receiver is started up. Default value is <code>6</code>
    </td></tr><tr id="Attributes_Common Attributes_maxIdleTime"><td><code class="attributeName">maxIdleTime</code></td><td>
      Maximum number of milliseconds of until Idle thread terminates. Default value is <code>60000</code> milliseconds.
    </td></tr><tr id="Attributes_Common Attributes_ooBInline"><td><code class="attributeName">ooBInline</code></td><td>
      Boolean value for the socket OOBINLINE option. Possible values are <code>true</code> or <code>false</code>.
    </td></tr><tr id="Attributes_Common Attributes_rxBufSize"><td><code class="attributeName">rxBufSize</code></td><td>
      The receiver buffer size on the receiving sockets. Value is in bytes, the default value is <code>65536</code> bytes.
    </td></tr><tr id="Attributes_Common Attributes_txBufSize"><td><code class="attributeName">txBufSize</code></td><td>
      The sending buffer size on the receiving sockets. Value is in bytes, the default value is <code>25188</code> bytes.
    </td></tr><tr id="Attributes_Common Attributes_udpRxBufSize"><td><code class="attributeName">udpRxBufSize</code></td><td>
        The receive buffer size on the datagram socket.
        Default value is <code>25188</code> bytes.
    </td></tr><tr id="Attributes_Common Attributes_udpTxBufSize"><td><code class="attributeName">udpTxBufSize</code></td><td>
       The send buffer size on the datagram socket.
       Default value is <code>65536</code> bytes.
    </td></tr><tr id="Attributes_Common Attributes_soKeepAlive"><td><code class="attributeName">soKeepAlive</code></td><td>
      Boolean value for the socket SO_KEEPALIVE option. Possible values are <code>true</code> or <code>false</code>.
    </td></tr><tr id="Attributes_Common Attributes_soLingerOn"><td><code class="attributeName">soLingerOn</code></td><td>
      Boolean value to determine whether to use the SO_LINGER socket option.
      Possible values are <code>true</code> or <code>false</code>. Default value is <code>true</code>.
    </td></tr><tr id="Attributes_Common Attributes_soLingerTime"><td><code class="attributeName">soLingerTime</code></td><td>
      Sets the SO_LINGER socket option time value. The value is in seconds.
      The default value is <code>3</code> seconds.
    </td></tr><tr id="Attributes_Common Attributes_soReuseAddress"><td><code class="attributeName">soReuseAddress</code></td><td>
     Boolean value for the socket SO_REUSEADDR option. Possible values are <code>true</code> or <code>false</code>.
    </td></tr><tr id="Attributes_Common Attributes_tcpNoDelay"><td><code class="attributeName">tcpNoDelay</code></td><td>
     Boolean value for the socket TCP_NODELAY option. Possible values are <code>true</code> or <code>false</code>.
     The default value is <code>true</code>
    </td></tr><tr id="Attributes_Common Attributes_timeout"><td><code class="attributeName">timeout</code></td><td>
     Sets the SO_TIMEOUT option on the socket. The value is in milliseconds and the default value is <code>3000</code>
     milliseconds.
    </td></tr><tr id="Attributes_Common Attributes_useBufferPool"><td><code class="attributeName">useBufferPool</code></td><td>
     Boolean value whether to use a shared buffer pool of cached <code>org.apache.catalina.tribes.io.XByteBuffer</code>
     objects. If set to true, the XByteBuffer that is used to pass a message up the channel, will be recycled at the end
     of the requests. This means that interceptors in the channel must not maintain a reference to the object
     after the <code>org.apache.catalina.tribes.ChannelInterceptor#messageReceived</code> method has exited.
    </td></tr></table>
  </div></div>
  <div class="subsection"><h4 id="NioReceiver">NioReceiver</h4><div class="text">
  </div></div>
  <div class="subsection"><h4 id="BioReceiver">BioReceiver</h4><div class="text">
  </div></div>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>