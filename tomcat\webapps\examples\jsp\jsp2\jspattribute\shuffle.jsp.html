<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;%@ taglib prefix="my" uri="http://tomcat.apache.org/jsp2-example-taglib"%>

&lt;html>
  &lt;head>
    &lt;title>JSP 2.0 Examples - Shuffle Example&lt;/title>
  &lt;/head>
  &lt;body>
    &lt;h1>JSP 2.0 Examples - Shuffle Example&lt;/h1>
    &lt;hr>
    &lt;p>Try reloading the page a few times.  Both the rows and the columns
    are shuffled and appear different each time.&lt;/p>
    &lt;p>Here's how the code works.  The SimpleTag handler called
    &amp;lt;my:shuffle&amp;gt; accepts three attributes.  Each attribute is a
    JSP Fragment, meaning it is a fragment of JSP code that can be
    dynamically executed by the shuffle tag handler on demand.  The
    shuffle tag handler executes the three fragments in a random order.
    To shuffle both the rows and the columns, the shuffle tag is used
    with itself as a parameter.&lt;/p>
    &lt;hr>
    &lt;blockquote>
     &lt;font color="#ffffff">
      &lt;table>
        &lt;my:shuffle>
          &lt;jsp:attribute name="fragment1">
            &lt;tr>
              &lt;my:shuffle>
                &lt;jsp:attribute name="fragment1">
                  &lt;my:tile color="#ff0000" label="A"/>
                &lt;/jsp:attribute>
                &lt;jsp:attribute name="fragment2">
                  &lt;my:tile color="#00ff00" label="B"/>
                &lt;/jsp:attribute>
                &lt;jsp:attribute name="fragment3">
                  &lt;my:tile color="#0000ff" label="C"/>
                &lt;/jsp:attribute>
              &lt;/my:shuffle>
            &lt;/tr>
          &lt;/jsp:attribute>
          &lt;jsp:attribute name="fragment2">
            &lt;tr>
              &lt;my:shuffle>
                &lt;jsp:attribute name="fragment1">
                  &lt;my:tile color="#ff0000" label="1"/>
                &lt;/jsp:attribute>
                &lt;jsp:attribute name="fragment2">
                  &lt;my:tile color="#00ff00" label="2"/>
                &lt;/jsp:attribute>
                &lt;jsp:attribute name="fragment3">
                  &lt;my:tile color="#0000ff" label="3"/>
                &lt;/jsp:attribute>
              &lt;/my:shuffle>
            &lt;/tr>
          &lt;/jsp:attribute>
          &lt;jsp:attribute name="fragment3">
            &lt;tr>
              &lt;my:shuffle>
                &lt;jsp:attribute name="fragment1">
                  &lt;my:tile color="#ff0000" label="!"/>
                &lt;/jsp:attribute>
                &lt;jsp:attribute name="fragment2">
                  &lt;my:tile color="#00ff00" label="@"/>
                &lt;/jsp:attribute>
                &lt;jsp:attribute name="fragment3">
                  &lt;my:tile color="#0000ff" label="#"/>
                &lt;/jsp:attribute>
              &lt;/my:shuffle>
            &lt;/tr>
          &lt;/jsp:attribute>
        &lt;/my:shuffle>
      &lt;/table>
     &lt;/font>
    &lt;/blockquote>
  &lt;/body>
&lt;/html>
</pre></body></html>