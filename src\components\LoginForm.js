import React, { Component } from 'react';
import { connect } from 'react-redux';
import TextField from '@material-ui/core/TextField';
import Button from '@material-ui/core/Button';
import Paper from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';
import { withStyles } from '@material-ui/core/styles';
import { actionLogin, actionClearAuthError } from '../actions/AuthActions';

const styles = theme => ({
    container: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        backgroundColor: '#f5f5f5'
    },
    paper: {
        padding: theme.spacing.unit * 4,
        maxWidth: 400,
        width: '100%',
        margin: theme.spacing.unit * 2
    },
    form: {
        display: 'flex',
        flexDirection: 'column'
    },
    title: {
        textAlign: 'center',
        marginBottom: theme.spacing.unit * 3
    },
    error: {
        color: theme.palette.error.main,
        textAlign: 'center',
        marginTop: theme.spacing.unit
    },
    button: {
        marginTop: theme.spacing.unit * 2
    },
    textField: {
        marginBottom: theme.spacing.unit * 2
    }
});

class LoginForm extends Component {
    constructor(props) {
        super(props);
        this.state = {
            username: '',
            password: ''
        };
    }

    componentDidMount() {
        // Clear any previous auth errors
        this.props.actionClearAuthError();
    }

    handleInputChange = (field) => (event) => {
        this.setState({
            [field]: event.target.value
        });
        
        // Clear error when user starts typing
        if (this.props.authError) {
            this.props.actionClearAuthError();
        }
    };

    handleSubmit = (event) => {
        event.preventDefault();

        const { username, password } = this.state;

        if (!username.trim() || !password.trim()) {
            return;
        }

        this.props.actionLogin(username.trim(), password);
    };

    handleKeyPress = (event) => {
        if (event.key === 'Enter') {
            this.handleSubmit(event);
        }
    };

    render() {
        const { classes, isLoggingIn, authError, selectedDatabase } = this.props;
        const { username, password } = this.state;

        return (
            <div className={classes.container}>
                <Paper className={classes.paper} elevation={3}>
                    <Typography variant="h4" className={classes.title}>
                        unTill JViewer
                    </Typography>

                    {selectedDatabase && (
                        <Typography variant="h6" style={{ marginBottom: 16, color: '#666' }}>
                            Database: {selectedDatabase.name}
                        </Typography>
                    )}

                    <form className={classes.form} onSubmit={this.handleSubmit}>
                        <TextField
                            label="Username"
                            value={username}
                            onChange={this.handleInputChange('username')}
                            onKeyPress={this.handleKeyPress}
                            disabled={isLoggingIn}
                            autoFocus
                            required
                            className={classes.textField}
                            fullWidth
                        />

                        <TextField
                            label="Password"
                            type="password"
                            value={password}
                            onChange={this.handleInputChange('password')}
                            onKeyPress={this.handleKeyPress}
                            disabled={isLoggingIn}
                            required
                            className={classes.textField}
                            fullWidth
                        />
                        
                        {authError && (
                            <Typography variant="body2" className={classes.error}>
                                {authError}
                            </Typography>
                        )}
                        
                        <Button
                            type="submit"
                            variant="contained"
                            color="primary"
                            className={classes.button}
                            disabled={isLoggingIn || !username.trim() || !password.trim()}
                        >
                            {isLoggingIn ? 'Logging in...' : 'Login'}
                        </Button>
                    </form>
                </Paper>
            </div>
        );
    }
}

const mapStateToProps = (state) => ({
    isLoggingIn: state.auth.isLoggingIn,
    authError: state.auth.error,
    selectedDatabase: state.auth.selectedDatabase
});

const mapDispatchToProps = {
    actionLogin,
    actionClearAuthError
};

export default connect(mapStateToProps, mapDispatchToProps)(withStyles(styles)(LoginForm));
