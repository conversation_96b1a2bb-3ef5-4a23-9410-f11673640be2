import _ from 'lodash';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { actionPeriodChangeFiltersValue, actionPeriodChangeFiltersValidation } from '../actions/MainActions';
import { addTabFilter } from '../actions/TabsActions';
import { withStyles } from '@material-ui/core/styles';
import TextField from '@material-ui/core/TextField';
import Icon from '@material-ui/core/Icon';
import Input from '@material-ui/core/Input';
import InputLabel from '@material-ui/core/InputLabel';
import MenuItem from '@material-ui/core/MenuItem';
import FormControl from '@material-ui/core/FormControl';
import Select from '@material-ui/core/Select';
import ListItemText from '@material-ui/core/ListItemText';
import Checkbox from '@material-ui/core/Checkbox';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Switch from '@material-ui/core/Switch';
import { rc } from '../lib/utils';
import { FILTERS } from '../const/filter';

const styles = (theme) => ({
    colorSwitchBase: {
        color: '#ffffff',
        '&$colorChecked': {
            color: '#e0f358',
            '& + $colorBar': {
                backgroundColor: '#ffffff',
            },
        },
    },
    addButton: {
        backgroundColor: '#e0f358'
    },
    colorBar: {
        backgroundColor: '#eeeeee'
    },
    colorChecked: {},
    colorPrimary: {
        color: '#ffffff'
    },
    root: {
        display: 'flex',
        flexWrap: 'wrap',
    },
    input: {
        color: '#ffffff',
        fontSize: '1em',
        textAlign: 'center',
    },
    select: {
        color: '#ffffff',
        fontSize: '1em'
    },
    formControl: {
        marginTop: 4,
        minWidth: '11rem'
    },
    textField: {
        marginBottom: 0,
        //width: '11rem',
    },
    button: {
        margin: theme.spacing.unit,
    },
});

class Filters extends Component {
    constructor(props) {
        super(props);

        this.state = {
            kinds: []
        };

        this.handleAddFilterSelect = this.handleAddFilterSelect.bind(this);
    }

    renderKindsItems() {
        const { strings } = this.props;

        if (_.isObject(strings) && strings.entryKinds) {
            const menuItems = [];
            for (const kind of Object.keys(this.props.strings.entryKinds)) {
                menuItems.push(
                    <MenuItem key={`mk${kind}`} id={`mi${kind}`} value={kind}>{this.props.strings.entryKinds[kind]}</MenuItem>
                );
            }

            return menuItems;
        }

        return [];
    }


    renderCancel(key) {
        if (this.props.values[key] !== "") {
            return (
                <Icon
                    className="cancelIcon"
                    onClick={() => {
                        this.props.actionPeriodChangeFiltersValue(this.props.sessionId, key, '');
                    }}>
                    cancel
                </Icon>
            );
        }

        return null;
    }

    renderKindFilter() {
        const { classes } = this.props;

        return (
            <div className="input-line">
                <FormControl className={classes.formControl}>
                    <InputLabel htmlFor="byKind1" className="inp-label2">{rc(this.props.strings, 'byKind')}</InputLabel>
                    <Select
                        className={classes.select}
                        disableUnderline
                        value={this.props.values["kind"]}
                        onChange={(event) => {
                            this.props.actionPeriodChangeFiltersValue(this.props.sessionId, 'kind', event.target.value);
                        }}
                        classes={{
                            icon: 'inp-arrow'
                        }}
                        inputProps={{
                            name: 'byKind',
                            id: 'byKind1',
                            className: 'inputTime',
                        }}
                    >
                        <MenuItem value="">{rc(this.props.strings, 'showAll')}</MenuItem>

                        {this.renderKindsItems()}
                    </Select>
                </FormControl>

                {this.renderCancel('kind')}
            </div>
        );
    }

    renderFilterItem(key, title, placeholder) {
        const { classes } = this.props;
        return (
            <div key={key} className="input-line">
                <TextField
                    id={key}
                    type="text"
                    label={title}
                    placeholder={placeholder}
                    className={classes.textField}
                    margin="normal"
                    value={this.props.values[key]}
                    onChange={(event) => {
                        this.props.actionPeriodChangeFiltersValue(this.props.sessionId, key, event.target.value);
                    }}
                    InputProps={{
                        disableUnderline: true,
                        className: classes.input
                    }}
                    InputLabelProps={{
                        className: 'inp-label2'
                    }}
                />

                {this.renderCancel(key)}
            </div>
        )
    }

    renderFilters() {
        const { filters } = this.props;

        if (_.isArray(filters)) {
            return filters.map((key) => {
                const f = FILTERS[key];

                return this.renderFilterItem(f.key, rc(this.props.strings, f.title), rc(this.props.strings, f.placeholder));
            });
        }

        return null;
    }

    handleAddFilterSelect(event) {
        const filters = event.target.value;

        if (_.isArray(filters)) {
            this.props.addTabFilter(this.props.sessionId, filters);
        }
    }

    renderAvailableFilters() {
        const { strings, filters, classes } = this.props;
        const availableFilters = Object.keys(FILTERS);

        if (_.size(availableFilters)) {
            const items = [];

            _.forEach(availableFilters, key => {
                let f = FILTERS[key];

                if (_.isObject(f)) {
                    items.push(
                        <MenuItem key={`ff${f.key}`} value={f.key}>
                            <Checkbox checked={filters ? filters.indexOf(f.key) > -1 : false} />
                            <ListItemText primary={rc(strings, f.title)} className={classes.select} />
                        </MenuItem>
                    );
                }
            });

            return items;
        }

        return [];
    }

    renderAddFilter() {
        const { classes, filters } = this.props;
        const items = this.renderAvailableFilters();

        if (!items || items.length === 0) {
            return null;
        }

        return (
            <div className="input-line">
                <FormControl className={classes.formControl}>
                    <InputLabel htmlFor="addFilter1" className="inp-label2">{rc(this.props.strings, 'moreFilters')}</InputLabel>
                    <Select
                        id="addFilter1"
                        className={classes.select}
                        disableUnderline
                        multiple
                        value={filters || []}
                        input={<Input />}
                        renderValue={(selected) => <span className={classes.select}>{rc(this.props.strings, 'filtersSelected', [ selected.length ])}</span>}
                        onChange={this.handleAddFilterSelect}
                        classes={{
                            icon: 'inp-arrow'
                        }}
                    >
                        {items}
                    </Select>
                </FormControl>
            </div>
        );
    }

    render() {
        const { classes } = this.props;

        return (
            <div>

                {this.renderKindFilter()}
                {this.renderFilters()}
                {this.renderAddFilter()}

                <div className="input-line2">
                    <FormControlLabel
                        classes={{
                            label: 'inp-switch-label'
                        }}
                        control={
                            <Switch
                                checked={this.props.validation}
                                onChange={(event) => {
                                    this.props.actionPeriodChangeFiltersValidation(this.props.sessionId, event.target.checked)
                                }}
                                classes={{
                                    switchBase: classes.colorSwitchBase,
                                    checked: classes.colorChecked,
                                    bar: classes.colorBar,
                                }}
                                color="default"
                            />
                        }
                        label={rc(this.props.strings, 'integrityCheck')}
                    />
                </div>
            </div>
        );
    }
}

const mapStateToProps = (state) => {
    const tab = state.tabs.items.find((tt) => tt.sessionId === state.tabs.active);
    const { filters: values, sessionId, validation, availableFilters: filters } = tab;
    const { strings } = state.global;

    return {
        filters,
        values,
        sessionId,
        strings,
        validation
    }
};

export default connect(mapStateToProps, {
    actionPeriodChangeFiltersValue,
    actionPeriodChangeFiltersValidation,
    addTabFilter
})(withStyles(styles)(Filters));