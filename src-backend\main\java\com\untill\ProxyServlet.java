package com.untill;

import com.untill.auth.BasicAuthUtils;
import com.untill.auth.UserRepository;
import com.untill.config.UntillConfigReader;
import com.untill.config.UntillConfigReader.UntillConfig;

import javax.servlet.*;
import javax.servlet.http.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Enumeration;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProxyServlet extends HttpServlet {
	private static final long serialVersionUID = 1L;

	protected static final Logger LOGGER = Logger.getLogger(ProxyServlet.class.getName());

	private String targetHost;
	private int targetPort;
	private UserRepository userRepository;

	@Override
	public void init() throws ServletException {
		super.init();

		// Initialize user repository
		userRepository = UserRepository.getInstance();

		// Load configuration from untill.ini
		loadFromUntillConfig();

		LOGGER.info("ProxyServlet initialized with target: " + targetHost + ":" + targetPort);
	}

	private void loadFromUntillConfig() {
		UntillConfig config = UntillConfigReader.getConfig();
		targetHost = config.getHost();
		targetPort = config.getPort();
		LOGGER.info("Loaded configuration from untill.ini: " + config);
	}

	@Override
	protected void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// Add CORS headers for development
		addCorsHeaders(response);

		// Handle preflight requests
		if ("OPTIONS".equals(request.getMethod())) {
			response.setStatus(HttpServletResponse.SC_OK);
			return;
		}

		// Check authentication
		if (!isAuthenticated(request)) {
			sendAuthenticationRequired(response);
			return;
		}

		// Cut proxy context from request URI
		String uriWOProxyContext = request.getRequestURI();
		if (uriWOProxyContext.startsWith(request.getContextPath())) {
			uriWOProxyContext = uriWOProxyContext.substring(request.getContextPath().length());
		}
		String targetUrl = "http://" + targetHost + ":" + targetPort + uriWOProxyContext;
		if (request.getQueryString() != null) {
			targetUrl += "?" + request.getQueryString();
		}

		LOGGER.info("Proxying " + request.getMethod() + " request to: " + targetUrl);

		// Open connection to target server
		HttpURLConnection connection = (HttpURLConnection) new URL(targetUrl).openConnection();
		connection.setRequestMethod(request.getMethod());

		// Set connection properties
		connection.setDoInput(true);
		if ("POST".equals(request.getMethod()) || "PUT".equals(request.getMethod()) || "PATCH".equals(request.getMethod())) {
			connection.setDoOutput(true);
		}

		// Copy headers from original request (excluding hop-by-hop headers)
		Enumeration<String> headerNames = request.getHeaderNames();
		while (headerNames.hasMoreElements()) {
			String headerName = headerNames.nextElement();
			String headerValue = request.getHeader(headerName);

			// Skip hop-by-hop headers
			if (!isHopByHopHeader(headerName)) {
				connection.setRequestProperty(headerName, headerValue);
				LOGGER.fine("Copied header: " + headerName + " = " + headerValue);
			}
		}

		// Copy request body if present
		if (connection.getDoOutput()) {
			try (InputStream input = request.getInputStream();
				 OutputStream output = connection.getOutputStream()) {
				byte[] buffer = new byte[8192];
				int bytesRead;
				while ((bytesRead = input.read(buffer)) != -1) {
					output.write(buffer, 0, bytesRead);
				}
				LOGGER.info("Request body forwarded to target server.");
			} catch (IOException e) {
				LOGGER.log(Level.SEVERE, "Error while forwarding request body", e);
				throw e;
			}
		}

		// Read response from target server
		try {
			response.setStatus(connection.getResponseCode());
			LOGGER.info("Response status from target server: " + connection.getResponseCode());

			// Copy response headers (excluding hop-by-hop headers)
			for (String headerName : connection.getHeaderFields().keySet()) {
				if (headerName != null && !isHopByHopHeader(headerName)) {
					String headerValue = connection.getHeaderField(headerName);
					response.setHeader(headerName, headerValue);
					LOGGER.fine("Copied response header: " + headerName + " = " + headerValue);
				}
			}

			// Copy response body
			InputStream input;
			try {
				input = connection.getInputStream();
			} catch (IOException e) {
				// If we get an error, try to read from error stream
				input = connection.getErrorStream();
				if (input == null) {
					throw e;
				}
			}

			try (InputStream responseInput = input;
				 OutputStream output = response.getOutputStream()) {
				byte[] buffer = new byte[8192];
				int bytesRead;
				while ((bytesRead = responseInput.read(buffer)) != -1) {
					output.write(buffer, 0, bytesRead);
				}
				LOGGER.info("Response body forwarded to client.");
			}
		} catch (IOException e) {
			LOGGER.log(Level.SEVERE, "Error while forwarding response", e);
			throw e;
		}
	}

	/**
	 * Check if header is a hop-by-hop header that should not be forwarded
	 */
	private boolean isHopByHopHeader(String headerName) {
		String lowerCaseName = headerName.toLowerCase();
		return "connection".equals(lowerCaseName)
			|| "keep-alive".equals(lowerCaseName)
			|| "proxy-authenticate".equals(lowerCaseName)
			|| "proxy-authorization".equals(lowerCaseName)
			|| "te".equals(lowerCaseName)
			|| "trailers".equals(lowerCaseName)
			|| "transfer-encoding".equals(lowerCaseName)
			|| "upgrade".equals(lowerCaseName);
	}

	/**
	 * Check if request is authenticated
	 */
	private boolean isAuthenticated(HttpServletRequest request) {
		BasicAuthUtils.Credentials credentials = BasicAuthUtils.extractCredentials(request);
		if (credentials == null) {
			LOGGER.fine("No credentials provided");
			return false;
		}

		return userRepository.authenticate(credentials.getUsername(), credentials.getPassword());
	}

	/**
	 * Add CORS headers for development
	 */
	private void addCorsHeaders(HttpServletResponse response) {
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, Accept");
		response.setHeader("Access-Control-Max-Age", "3600");
	}

	/**
	 * Send 401 Unauthorized response without WWW-Authenticate header
	 * to prevent browser's built-in authentication dialog
	 */
	private void sendAuthenticationRequired(HttpServletResponse response) throws IOException {
		response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
		// Don't send WWW-Authenticate header to avoid browser's auth dialog
		response.setContentType("application/json");
		response.setCharacterEncoding("UTF-8");
		response.getWriter().write("{\"error\":\"Authentication required\",\"message\":\"Please provide valid credentials\"}");
		LOGGER.info("Authentication required - sent 401 response");
	}
}
