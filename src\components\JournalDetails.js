import _ from 'lodash';
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import Button from '@material-ui/core/Button';

import { rc } from '../lib/utils';

class JournalDetails extends PureComponent {
    constructor(props) {
        super(props);

        this.handleClose = this.handleClose.bind(this)
    }

    handleClose() {
        const { onClose } = this.props;

        if (_.isFunction(onClose)) {
            onClose();
        }
    }

    render() {
        const { open, data } = this.props;
        const { strings, ticket, changedValues, fields, hasTicket, hasChanges, hasFields } = data;

        if (!open) return null;

        return (
            <Dialog
                open={true}
                onClose={this.handleClose}
                scroll='paper'
                maxWidth='md'
                aria-labelledby="scroll-dialog-title"
            >
                <DialogContent>
                    <div style={{ display: hasTicket ? '' : 'none' }}>
                        <h2>{rc(strings, 'printout')}</h2>
                        <pre>
                            {ticket}
                        </pre>
                    </div>
                    <div style={{ display: hasChanges ? '' : 'none' }}>
                        <h2>{rc(strings, 'changes')}</h2>
                        <div className="flex_table">
                            <div className="row">
                                <div className="cell label header">{rc(strings, 'field')}</div>
                                <div className="cell value header">{rc(strings, 'oldValue')}</div>
                                <div className="cell value header">{rc(strings, 'newValue')}</div>
                            </div>
                            

                            {
                                changedValues.map((n, idx) => {
                                    return (
                                        <div className="row">
                                            <div className="cell label">
                                                {n.name}
                                            </div>
                                            <div className="cell value">{n.oldValue}</div>
                                            <div className="cell value">{n.newValue}</div>
                                        </div>
                                    );
                                })
                            }
                        </div>
                    </div>
                    <div style={{ display: hasFields ? '' : 'none' }}>
                        <h2>{rc(strings, 'details')}</h2>
                        <div className="flex_table">
                            {Object.keys(fields).map((n, idx) => {
                                return (
                                    <div className="row">
                                        <div className="cell label">
                                            {n}
                                        </div>
                                        <div className="cell value">
                                            {fields[n]}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </DialogContent>
                <DialogActions>
                    <Button
                        onClick={this.handleClose}
                        color="secondary">
                        {rc(strings, 'ok')}
                    </Button>
                </DialogActions>
            </Dialog>
        );
    }
}

JournalDetails.propTypes = {
    open: PropTypes.bool,
    data: PropTypes.object,
    onClose: PropTypes.func
};

export default JournalDetails;