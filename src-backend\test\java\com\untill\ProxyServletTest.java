package com.untill;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.Enumeration;

import static org.mockito.Mockito.*;
import static org.mockito.Mockito.atLeast;

public class ProxyServletTest {

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private ServletConfig servletConfig;

    private ProxyServlet proxyServlet;

    @Before
    public void setUp() throws ServletException {
        MockitoAnnotations.openMocks(this);
        proxyServlet = new ProxyServlet();
        
        // Mock servlet config
        when(servletConfig.getInitParameter("targetHost")).thenReturn("http://localhost");
        when(servletConfig.getInitParameter("targetPort")).thenReturn("8080");
        
        proxyServlet.init(servletConfig);
    }

    @Test
    public void testInitWithDefaultValues() throws ServletException {
        ProxyServlet servlet = new ProxyServlet();
        ServletConfig config = mock(ServletConfig.class);
        when(config.getInitParameter("targetHost")).thenReturn(null);
        when(config.getInitParameter("targetPort")).thenReturn(null);
        
        servlet.init(config);
        // Test passes if no exception is thrown
    }

    @Test
    public void testIsHopByHopHeader() throws Exception {
        // Use reflection to test private method
        java.lang.reflect.Method method = ProxyServlet.class.getDeclaredMethod("isHopByHopHeader", String.class);
        method.setAccessible(true);
        
        // Test hop-by-hop headers
        assert((Boolean) method.invoke(proxyServlet, "Connection"));
        assert((Boolean) method.invoke(proxyServlet, "keep-alive"));
        assert((Boolean) method.invoke(proxyServlet, "TRANSFER-ENCODING"));
        
        // Test regular headers
        assert(!(Boolean) method.invoke(proxyServlet, "Content-Type"));
        assert(!(Boolean) method.invoke(proxyServlet, "Authorization"));
    }

    @Test
    public void testServiceMethodSetup() throws ServletException, IOException {
        // Mock request
        when(request.getMethod()).thenReturn("GET");
        when(request.getRequestURI()).thenReturn("/api/test");
        when(request.getQueryString()).thenReturn("param=value");
        when(request.getHeaderNames()).thenReturn(Collections.enumeration(Collections.singletonList("Content-Type")));
        when(request.getHeader("Content-Type")).thenReturn("application/json");
        when(request.getInputStream()).thenReturn(new MockServletInputStream(new ByteArrayInputStream("".getBytes())));
        
        // Mock response
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        when(response.getOutputStream()).thenReturn(new MockServletOutputStream(outputStream));
        
        // Note: This test will fail when trying to connect to localhost:8080
        // but it tests the setup part of the service method
        try {
            proxyServlet.service(request, response);
        } catch (Exception e) {
            // Expected to fail due to connection issues in test environment
            // The important part is that the method doesn't fail due to setup issues
        }
        
        verify(request, atLeast(1)).getMethod();
        verify(request).getRequestURI();
        verify(request, atLeast(1)).getQueryString();
    }

    // Helper classes for mocking
    private static class MockServletInputStream extends javax.servlet.ServletInputStream {
        private final ByteArrayInputStream inputStream;

        public MockServletInputStream(ByteArrayInputStream inputStream) {
            this.inputStream = inputStream;
        }

        @Override
        public int read() throws IOException {
            return inputStream.read();
        }

        @Override
        public boolean isFinished() {
            return inputStream.available() == 0;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(javax.servlet.ReadListener readListener) {
            // Not implemented for test
        }
    }

    private static class MockServletOutputStream extends javax.servlet.ServletOutputStream {
        private final ByteArrayOutputStream outputStream;

        public MockServletOutputStream(ByteArrayOutputStream outputStream) {
            this.outputStream = outputStream;
        }

        @Override
        public void write(int b) throws IOException {
            outputStream.write(b);
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setWriteListener(javax.servlet.WriteListener writeListener) {
            // Not implemented for test
        }
    }
}
