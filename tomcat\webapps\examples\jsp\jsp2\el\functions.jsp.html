<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;%@page contentType="text/html; charset=UTF-8" %>
&lt;%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
&lt;%@ taglib prefix="my" uri="http://tomcat.apache.org/jsp2-example-taglib"%>

&lt;html>
  &lt;head>
    &lt;title>JSP 2.0 Expression Language - Functions&lt;/title>
  &lt;/head>
  &lt;body>
    &lt;h1>JSP 2.0 Expression Language - Functions&lt;/h1>
    &lt;hr>
    An upgrade from the JSTL expression language, the JSP 2.0 EL also
    allows for simple function invocation.  Functions are defined
    by tag libraries and are implemented by a Java programmer as
    static methods.

    &lt;blockquote>
      &lt;u>&lt;b>Change Parameter&lt;/b>&lt;/u>
      &lt;form action="functions.jsp" method="GET">
          foo = &lt;input type="text" name="foo" value="${fn:escapeXml(param["foo"])}">
          &lt;input type="submit">
      &lt;/form>
      &lt;br>
      &lt;code>
        &lt;table border="1">
          &lt;thead>
            &lt;td>&lt;b>EL Expression&lt;/b>&lt;/td>
            &lt;td>&lt;b>Result&lt;/b>&lt;/td>
          &lt;/thead>
          &lt;tr>
            &lt;td>\${param["foo"]}&lt;/td>
            &lt;td>${fn:escapeXml(param["foo"])}&amp;nbsp;&lt;/td>
          &lt;/tr>
          &lt;tr>
            &lt;td>\${my:reverse(param["foo"])}&lt;/td>
            &lt;td>${my:reverse(fn:escapeXml(param["foo"]))}&amp;nbsp;&lt;/td>
          &lt;/tr>
          &lt;tr>
            &lt;td>\${my:reverse(my:reverse(param["foo"]))}&lt;/td>
            &lt;td>${my:reverse(my:reverse(fn:escapeXml(param["foo"])))}&amp;nbsp;&lt;/td>
          &lt;/tr>
          &lt;tr>
            &lt;td>\${my:countVowels(param["foo"])}&lt;/td>
            &lt;td>${my:countVowels(fn:escapeXml(param["foo"]))}&amp;nbsp;&lt;/td>
          &lt;/tr>
        &lt;/table>
      &lt;/code>
    &lt;/blockquote>
  &lt;/body>
&lt;/html>

</pre></body></html>