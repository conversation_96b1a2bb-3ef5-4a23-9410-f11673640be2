<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 Configuration Reference (9.0.106) - The Cluster object</title><meta name="author" content="Filip Hanik"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9 Configuration Reference</h1><div class="versionInfo">
            Version 9.0.106,
            <time datetime="2025-06-05">Jun 5 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="runtime-attributes.html">Runtime attributes</a></li><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">JASPIC</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>The Cluster object</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Security">Security</a></li><li><a href="#Engine_vs_Host_placement">Engine vs Host placement</a></li><li><a href="#Context_Attribute_Replication">Context Attribute Replication</a></li><li><a href="#Nested_Components">Nested Components</a></li><li><a href="#Attributes">Attributes</a><ol><li><a href="#SimpleTcpCluster_Attributes">SimpleTcpCluster Attributes</a></li></ol></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">
  <p>
  The tomcat cluster implementation provides session replication, context attribute replication and
  cluster wide WAR file deployment.
  While the <code>Cluster</code> configuration is fairly complex, the default configuration will work
  for most people out of the box. </p><p>
  The Tomcat Cluster implementation is very extensible, and hence we have exposed a myriad of options,
  making the configuration seem like a lot, but don't lose faith, instead you have a tremendous control
  over what is going on.</p>
</div><h3 id="Security">Security</h3><div class="text">

<p>The cluster implementation is written on the basis that a secure, trusted
network is used for all of the cluster related network traffic. It is not safe
to run a cluster on a insecure, untrusted network.</p>

<p>There are many options for providing a secure, trusted network for use by a
Tomcat cluster. These include:</p>
<ul>
  <li>private LAN</li>
  <li>a Virtual Private Network (VPN)</li>
  <li>IPSEC</li>
</ul>

<p>The <a href="cluster-interceptor.html#org.apache.catalina.tribes.group.interceptors.EncryptInterceptor_Attributes">EncryptInterceptor</a>
provides confidentiality and integrity protection but it does not protect
against all risks associated with running a Tomcat cluster on an untrusted
network, particularly DoS attacks.</p>

</div><h3 id="Engine_vs_Host_placement">Engine vs Host placement</h3><div class="text">
  <p>
   You can place the <code>&lt;Cluster&gt;</code> element inside either the <code>&lt;Engine&gt;</code>
   container or the <code>&lt;Host&gt;</code> container.<br>
   Placing it in the engine, means that you will support clustering in all virtual hosts of Tomcat,
   and share the messaging component. When you place the <code>&lt;Cluster&gt;</code> inside the <code>&lt;Engine&gt;</code>
   element, the cluster will append the host name of each session manager to the managers name so that two contexts with
   the same name but sitting inside two different hosts will be distinguishable.
  </p>
</div><h3 id="Context_Attribute_Replication">Context Attribute Replication</h3><div class="text">
  <p>To configure context attribute replication, simply do this by swapping out the context implementation
  used for your application context.</p>
  <div class="codeBox"><pre><code>&lt;Context className="org.apache.catalina.ha.context.ReplicatedContext"/&gt;</code></pre></div>
  <p>
    This context extends the Tomcat <code><a href="context.html">StandardContext</a></code>
    so all the options from the <a href="context.html">base implementation</a> are valid.
  </p>
</div><h3 id="Nested_Components">Nested Components</h3><div class="text">
  <p><b><a href="cluster-manager.html">Manager</a>:</b> <br>
    The session manager element identifies what kind of session manager is used in this cluster implementation.
    This manager configuration is identical to the one you would use in a regular <code><a href="context.html#Nested_Components">&lt;Context&gt;</a></code> configuration.
    <br>The default value is the <code>org.apache.catalina.ha.session.DeltaManager</code> that is closely coupled with
    the <code>SimpleTcpCluster</code> implementation. Other managers like the <code>org.apache.catalina.ha.session.BackupManager</code>
    are/could be loosely coupled and don't rely on the <code>SimpleTcpCluster</code> for its data replication.
  </p>
  <p><b><a href="cluster-channel.html">Channel</a>:</b> <br>
    The Channel and its sub components are all part of the IO layer
    for the cluster group, and is a module in it's own that we have nick named "Tribes"
    <br>
    Any configuring and tuning of the network layer, the messaging and the membership logic
    will be done in the channel and its nested components.
    You can always find out more about <a href="../tribes/introduction.html">Apache Tribes</a>
  </p>
  <p><b><a href="cluster-valve.html">Valve</a>:</b> <br>
    The Tomcat Cluster implementation uses <code>Tomcat <a href="valve.html">Valves</a></code> to
    track when requests enter and exit the servlet container. It uses these valves to be able to make
    intelligent decisions on when to replicate data, which is always at the end of a request.
  </p>
  <p><b><a href="cluster-deployer.html">Deployer</a>:</b> <br>
    The Deployer component is the Tomcat Farm Deployer. It allows you to deploy and undeploy applications
    cluster wide.
  </p>
  <p><b><a href="cluster-listener.html">ClusterListener</a>:</b> <br>
    ClusterListener's are used to track messages sent and received using the <code>SimpleTcpCluster</code>.
    If you wish to track messages, you can add a listener here, or you can add a valve to the channel object.
  </p>
</div><h3 id="Attributes">Attributes</h3><div class="text">
  <div class="subsection"><h4 id="SimpleTcpCluster_Attributes">SimpleTcpCluster Attributes</h4><div class="text">
  <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_SimpleTcpCluster Attributes_className"><td><strong><code class="attributeName">className</code></strong></td><td>
      <p>The main cluster class, currently only one is available,
         <code>org.apache.catalina.ha.tcp.SimpleTcpCluster</code>
      </p>
    </td></tr><tr id="Attributes_SimpleTcpCluster Attributes_channelSendOptions"><td><strong><code class="attributeName">channelSendOptions</code></strong></td><td>
      <p>The Tribes channel send options, default is <code>8</code>.<br>
         This option is used to set the flag that all messages sent through the
         SimpleTcpCluster uses. The flag decides how the messages are sent, and is a simple logical OR.</p>

      <div class="codeBox"><pre><code>int options = Channel.SEND_OPTIONS_ASYNCHRONOUS |
              Channel.SEND_OPTIONS_SYNCHRONIZED_ACK |
              Channel.SEND_OPTIONS_USE_ACK;</code></pre></div>
      <p>Some of the values are:<br>
      <code>Channel.SEND_OPTIONS_SYNCHRONIZED_ACK = 0x0004</code><br>
      <code>Channel.SEND_OPTIONS_ASYNCHRONOUS = 0x0008</code><br>
      <code>Channel.SEND_OPTIONS_USE_ACK = 0x0002</code><br>
      So to use ACK and ASYNC messaging, the flag would be <code>10</code> (8+2)
      <br>
      Note that if you use ASYNC messaging it is possible for update messages
      for a session to be processed by the receiving nodes in a different order
      to the order in which they were sent.
      </p>
      <p>
        The various <code>channelSendOptions</code> values offer a tradeoff
        between throughput on the sending node and the reliability of
        replication should the sending or receiving node(s) fail. Here are
        some common options. "Message" could be any message sent between nodes,
        but only session-change messages are considered, here.
      </p>
      <p>
        <code>channelSendOptions="8"</code> / <code>channelSendOptions="async"</code>
        As far as the sender is concerned, the message is "sent"
        as soon as it has been placed in the queue on the sender for
        transmission to the other nodes. The message may not reach any or all
        of the recipient nodes and may not be successfully processed on any
        nodes that it does reach. This option offers the highest throughput on
        the sender but the least reliability, as the triggering request will
        complete without any knowledge of the success/failure of the session
        replication.
      </p>
      <p>
        <code>channelSendOptions="2"</code> / <code>channelSendOptions="use_ack"</code>
        The sender will block the completion of the current request until all
        of the receiving nodes have acknowledged that they have received the
        message, but have not necessarily processed that message. This option
        will result in lower throughput on the sending node, because the message
        must be transmitted and the acknowledgement received, but the
        reliability is greater than the asynchronous model.
      </p>
      <p>
        <code>channelSendOptions="6"</code> / <code>channelSendOptions="sync,use_ack"</code>
        The sender will block the completion of the current request until
        all of the receiving nodes have acknowledged that they have received
        <u>and</u> processed the message. This option will have the lowest
        throughput (of these three) but the greatest reliability.
      </p>
      <p>
      You may also set these options as a comma separated string, e.g. "async, multicast", which
      will be translated into <code>Channel.SEND_OPTIONS_ASYNCHRONOUS | Channel.SEND_OPTIONS_MULTICAST</code>
      <br>
      The valid option names are "asynchronous" (alias "async"), "byte_message" (alias "byte")
      , "multicast", "secure", "synchronized_ack" (alias "sync"), "udp", "use_ack"
      </p>
    </td></tr><tr id="Attributes_SimpleTcpCluster Attributes_channelStartOptions"><td><code class="attributeName">channelStartOptions</code></td><td>
      <p>Sets the start and stop flags for the &lt;Channel&gt; object used by the cluster.
         The default is <code>Channel.DEFAULT</code> which starts all the channel services, such as
         sender, receiver, membership sender and membership receiver.
         The following flags are available today:</p>
         <div class="codeBox"><pre><code>Channel.DEFAULT = Channel.SND_RX_SEQ (1) |
                  Channel.SND_TX_SEQ (2) |
                  Channel.MBR_RX_SEQ (4) |
                  Channel.MBR_TX_SEQ (8);</code></pre></div>
      <p>When using the static membership service
      <code>org.apache.catalina.tribes.membership.StaticMembershipService</code>
      you must ensure that this attribute is configured to use the default
      value.</p>
    </td></tr><tr id="Attributes_SimpleTcpCluster Attributes_heartbeatBackgroundEnabled"><td><code class="attributeName">heartbeatBackgroundEnabled</code></td><td>
      <p>Flag whether invoke channel heartbeat at container background thread. Default value is false.
         Enable this flag don't forget to disable the channel heartbeat thread.
      </p>
    </td></tr><tr id="Attributes_SimpleTcpCluster Attributes_notifyLifecycleListenerOnFailure"><td><code class="attributeName">notifyLifecycleListenerOnFailure</code></td><td>
      <p>Flag whether notify LifecycleListeners if all ClusterListener couldn't accept channel message.
         Default value is false.
      </p>
    </td></tr></table>
  </div></div>
</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>